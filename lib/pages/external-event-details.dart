import 'dart:convert';
import 'dart:core';

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/external-event.model.dart';
import 'package:thermassist_mobile/models/gridster.model.dart';
import 'package:thermassist_mobile/parts/drawer.dart';
import 'package:thermassist_mobile/parts/internal-event-item.dart';
import 'package:thermassist_mobile/parts/widgets/announcement-widget.dart';
import 'package:thermassist_mobile/parts/widgets/clock-widget.dart';
import 'package:thermassist_mobile/parts/widgets/days-widget.dart';
import 'package:thermassist_mobile/parts/widgets/did-you-know-widget.dart';
import 'package:thermassist_mobile/parts/widgets/image-widget.dart';
import 'package:thermassist_mobile/parts/widgets/tides-widget.dart';
import 'package:thermassist_mobile/parts/widgets/video-widget.dart';
import 'package:thermassist_mobile/services/internal-event.service.dart';
import 'package:thermassist_mobile/services/settings.service.dart';
import 'package:url_launcher/url_launcher.dart';

import '../config/config.dart';
import '../models/internal-event.model.dart';
import '../parts/widgets/events-widget.dart';
import '../parts/widgets/weather-widget.dart';
import '../services/auth.service.dart';

class ExternalEventDetailsPage extends StatefulWidget {
  final ExternalEvent externalEvent;

  ExternalEventDetailsPage({required this.externalEvent});

  @override
  _ExternalEventDetailsPageState createState() => _ExternalEventDetailsPageState();
}

class _ExternalEventDetailsPageState extends State<ExternalEventDetailsPage> {

  @override
  initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton.icon(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
              foregroundColor: MaterialStateProperty.all(Colors.white),
            ),
            label: Text('Retour'),
          ),
          const SizedBox(
              width: 8
          ),
          Visibility(
            visible: widget.externalEvent.linkexternalevent != null,
            child: ElevatedButton.icon(
              icon: Icon(Icons.link),
              onPressed: () {
                launch(widget.externalEvent.linkexternalevent!);
              },
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
                foregroundColor: MaterialStateProperty.all(Colors.white),
              ),
              label: Text('Plus d\'informations'),
            ),
          ),
        ],
      ),
      appBar: AppBar(
        title: Text(widget.externalEvent.titleexternalevent,
          style: const TextStyle(
            color: Colors.white
          )
        ),
        iconTheme: IconThemeData(
          color: Colors.white
        ),
        backgroundColor: Color(0xff4e51ec),
      ),
      drawer: CustomDrawer(),
      body: Column(
        children: [
          Container(
            height: 100, // Adjust the banner height as needed
            decoration: BoxDecoration(
              image: DecorationImage(
                image: NetworkImage(widget.externalEvent.picexternalevent == null ? "https://images.prismic.io/peopleforbikes/e4073026-1530-40d8-a2d8-6d2b88ff2874_BB_0504.png?auto=compress,format" : "${AppConfig.baseUrl}/img/events/internal/${widget.externalEvent.picexternalevent!}"), // Replace with your image asset
                fit: BoxFit.cover
              ),
            ),
            child: Stack(
              children: [
                Container(
                  color: Colors.black.withOpacity(0.5), // Darkening overlay
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.externalEvent.titleexternalevent, // Replace with your banner text
                          textAlign: TextAlign.start,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20.0,
                              fontWeight: FontWeight.bold
                          ),
                        ),
                        Text(
                          widget.externalEvent.summaryexternalevent, // Replace with your banner text
                          textAlign: TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 15.0
                          ),
                        )
                      ]
                    )
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
                child: Html(
                  data: widget.externalEvent.descexternalevent,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }


}