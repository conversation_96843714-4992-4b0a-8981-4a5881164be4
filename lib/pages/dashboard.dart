import 'dart:convert';
import 'dart:core';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/gridster.model.dart';
import 'package:thermassist_mobile/parts/drawer.dart';
import 'package:thermassist_mobile/parts/widgets/announcement-widget.dart';
import 'package:thermassist_mobile/parts/widgets/clock-widget.dart';
import 'package:thermassist_mobile/parts/widgets/days-widget.dart';
import 'package:thermassist_mobile/parts/widgets/did-you-know-widget.dart';
import 'package:thermassist_mobile/parts/widgets/image-widget.dart';
import 'package:thermassist_mobile/parts/widgets/tides-widget.dart';
import 'package:thermassist_mobile/parts/widgets/video-widget.dart';
import 'package:thermassist_mobile/services/settings.service.dart';

import '../parts/widgets/events-widget.dart';
import '../parts/widgets/weather-widget.dart';
import '../services/auth.service.dart';

class DashboardPage extends StatefulWidget {
  @override
  _DashboardPageState createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {

  String? jwt;
  List<Gridster>? widgets;

  @override
  initState() {
    super.initState();
    _getToken();
  }

  void _getToken() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    setState(() {
      jwt = token;
    });
    _getEstablishmentSettings();
  }

  Future<List<Gridster>> _getEstablishmentSettings() async {
    try {
      final settingsService = SettingsService();
      final settings = await settingsService.getCurrentSettings();
      Iterable decodedWidgets = json.decode(settings.dashboardsettings);
      List<Gridster> gridster = List<Gridster>.from(decodedWidgets.map((model)=> Gridster.fromJson(model)));
      gridster.sort((a, b) {
        final xComparison = a.x.compareTo(b.x);
        if (xComparison != 0) {
          return xComparison;
        }
        return a.y.compareTo(b.y);
      });
      return gridster;
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accueil',
          style: TextStyle(
            color: Colors.white
          )
        ),
        iconTheme: IconThemeData(
          color: Colors.white
        ),
        backgroundColor: Color(0xff4e51ec),
      ),
      drawer: CustomDrawer(),
      body: FutureBuilder<List<Gridster>>(
        future: _getEstablishmentSettings(),
        builder: (context, snapshot) {

          if (snapshot.connectionState == ConnectionState.waiting) {
            // While data is being fetched, display a loading indicator
            return CircularProgressIndicator();
          } else if (snapshot.hasError) {
            // If there's an error, display an error message
            return Text('Error: ${snapshot.error}');
          } else if (!snapshot.hasData) {
            // If no data is available, display a message
            return Text('Aucune donnée disponible');
          }

          final widgets = snapshot.data;

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children:
              widgets != null && widgets.isNotEmpty
                  ?
              widgets!.map((widgetItem) {
                // Build the appropriate widget based on the widget type
                Widget widget;

                if (widgetItem.widget.codewidget == 'ANNOUNCEMENT') {
                  widget = AnnouncementWidget(content: widgetItem.widget.extra['content'],);
                } else if (widgetItem.widget.codewidget == 'DID_YOU_KNOW') {
                  widget = DidYouKnowWidget();
                } else if (widgetItem.widget.codewidget == 'IMAGE') {
                  widget = ImageWidget(url: widgetItem.widget.extra['image'], title: widgetItem.widget.extra['title'], link: widgetItem.widget.extra['link']);
                } else if (widgetItem.widget.codewidget == 'VIDEO') {
                  widget = VideoWidget(url: widgetItem.widget.extra['link']);
                } else if (widgetItem.widget.codewidget == 'CLOCK') {
                  widget = ClockWidget();
                } else if (widgetItem.widget.codewidget == 'DAYS') {
                  widget = DaysWidget();
                } else if (widgetItem.widget.codewidget == 'TIDES') {
                  widget = TidesWidget();
                } else if (widgetItem.widget.codewidget == 'WEATHER') {
                  widget = WeatherWidget();
                } else if (widgetItem.widget.codewidget == 'RECENT_EVENTS') {
                  widget = EventsWidget();
                } else {
                  // Handle other widget types
                  widget = Text('Unsupported Widget Type: ${widgetItem.widget.codewidget}');
                }

                return widget;
              }).toList()
                  :
              [
                SizedBox(
                  height: 128.0,
                ),
                // If no widgets, display a Text widget
                Text('Aucun widget n\'a pour l\'instant été configuré.',
                  style: TextStyle(
                    fontSize: 16.0
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      )
    );
  }


}