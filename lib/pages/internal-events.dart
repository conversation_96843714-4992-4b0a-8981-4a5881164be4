import 'dart:convert';
import 'dart:core';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:thermassist_mobile/pages/internal-event-details.dart';
import 'package:thermassist_mobile/parts/drawer.dart';
import 'package:thermassist_mobile/parts/internal-event-item.dart';
import 'package:thermassist_mobile/services/internal-event.service.dart';
import 'package:thermassist_mobile/services/reservation.service.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/basket-item.model.dart';
import '../models/internal-event-price.model.dart';
import '../models/internal-event-status.model.dart';
import '../models/internal-event.model.dart';
import '../models/service-config.model.dart';
import '../services/settings.service.dart';
import '../storage/basket.storage.dart';

class InternalEventsPage extends StatefulWidget {
  @override
  _InternalEventsPageState createState() => _InternalEventsPageState();
}

class _InternalEventsPageState extends State<InternalEventsPage> {

  List<InternalEvent>? internalEvents;
  Map<int, InternalEventStatus> internalEventStatuses = <int, InternalEventStatus>{};
  List<ServiceConfig>? serviceSettings;

  @override
  initState() {
    super.initState();
    _fetchInternalEvents();
    _fetchSettings();
  }

  void _fetchSettings() async {
    try {
      final settingsService = SettingsService();
      final settingsData = await settingsService.getCurrentSettings();
      setState(() {
        if (settingsData.servicessettings != null) {
          serviceSettings = (jsonDecode(settingsData.servicessettings) as List)
              .map((e) => ServiceConfig.fromJson(e)).toList();
        }
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _fetchInternalEvents() async {
    final InternalEventService internalEventService = InternalEventService();
    final internalEventsData = await internalEventService.getCurrentInternalEvents();
    setState(() {
      internalEvents = internalEventsData;
      _fetchInternalEventStatuses();
    });
  }

  void _fetchInternalEventStatuses() async {
    final InternalEventService internalEventService = InternalEventService();
    for (InternalEvent internalEvent in internalEvents!) {
      final internalEventStatus = await internalEventService.getInternalEventStatus(internalEvent.idinternalevent);
      setState(() {
        internalEventStatuses[internalEvent.idinternalevent] = internalEventStatus;
        print(internalEventStatuses);
      });
    }
  }

  Future<void> _showBasketBottomSheet(BuildContext context) async {
    List<BasketItem> basketItems = await BasketStorage.loadBasketItems();

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      constraints: const BoxConstraints(
        maxWidth: double.infinity,
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return SafeArea(
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height / 2,
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Panier',
                                style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                'Total: ' + _calculateTotalPrice(basketItems).toString() + '€',
                                style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 16.0),
                        if (basketItems.isEmpty)
                          Expanded(
                            child: Center(
                              child: Text(
                                'Vous n\'avez aucun article dans votre panier.',
                                style: TextStyle(fontSize: 16.0, color: Colors.grey),
                              ),
                            ),
                          ),
                        if (basketItems.isNotEmpty)
                          Expanded(
                            child: ListView.builder(
                              itemCount: basketItems.length,
                              itemBuilder: (context, index) {
                                BasketItem item = basketItems[index];
                                InternalEvent? event = findInternalEventById(item.internalEventId);
                                return ListTile(
                                  title: Text(event!.titleinternalevent),
                                  subtitle: Text(getPriceByIndex(item.internalEventId, item.pricingId).toString() + '€'),
                                  trailing: IconButton(
                                    icon: Icon(Icons.delete, color: Colors.black54),
                                    onPressed: () {
                                      setState(() {
                                        basketItems.removeAt(index);
                                      });
                                      _removeItemFromBasket(index, context);
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: ElevatedButton(
                            onPressed: basketItems.isNotEmpty ? () async {
                              final ReservationService reservationService = ReservationService();
                              final checkoutLink = await reservationService.payForReservation(basketItems);
                              BasketStorage.clearBasket();
                              _openBrowser(checkoutLink);
                              Navigator.pop(context); // Close the bottom sheet after checkout
                            } : null,
                            child: Text('Passer au paiement'),
                            style: ElevatedButton.styleFrom(
                              minimumSize: Size(double.infinity, 50),
                              backgroundColor: const Color(0xff585bef),
                              foregroundColor: Colors.white
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }
        );
      },
    );
  }

  void _openBrowser(String url) async {
    print(url);
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  Future<void> _removeItemFromBasket(int index, BuildContext context) async {
    List<BasketItem> basketItems = await BasketStorage.loadBasketItems();
    basketItems.removeAt(index);
    await BasketStorage.saveBasketItems(basketItems);
  }

  double _calculateTotalPrice(List<BasketItem> basketItems) {
    // Implement your logic to calculate total price based on items in the basket
    double totalPrice = 0.0;
    for (BasketItem item in basketItems) {
      // Assume you have a method to get price by index
      totalPrice += getPriceByIndex(item.internalEventId, item.pricingId);
    }
    return totalPrice;
  }

  double getPriceByIndex(int internalEventId, int index) {
    InternalEvent? event = findInternalEventById(internalEventId);
    if (event == null) return 0.0;
    List<InternalEventPrice> prices = parsePrices(event!.pricesinternalevent!);
    return prices[index].price!; // Dummy price
  }

  InternalEvent? findInternalEventById(int id) {
    for (InternalEvent event in internalEvents!) {
      if (event.idinternalevent == id) return event;
    }
    return null;
  }

  List<InternalEventPrice> parsePrices(String pricesJson) {
    List<dynamic> jsonList = jsonDecode(pricesJson);
    return jsonList.map((json) => InternalEventPrice.fromJson(json)).toList();
  }

  bool _canDisplay(String key) {
    if (serviceSettings == null) return true;
    for (var service in serviceSettings!) {
      print(service.name);
      if (service.name == key) {
        return service.status!;
      }
    }
    // If the service with the given ID is not found, return true by default
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Activités Internes',
          style: TextStyle(
            color: Colors.white
          )
        ),
        iconTheme: IconThemeData(
          color: Colors.white
        ),
        backgroundColor: Color(0xff4e51ec),
        actions: [
          if (_canDisplay("Paiement en ligne"))
          GestureDetector(
            onTap: () {
              _showBasketBottomSheet(context); // Trigger the bottom sheet
            },
            child: const Row(
              children: [
                Icon(Icons.shopping_basket, color: Colors.white),
                SizedBox(width: 5), // Add spacing between the icon and text
                Text(
                  'Panier',
                  style: TextStyle(
                    color: Colors.white, // Ensure the text is visible
                    fontSize: 16.0,
                  ),
                ),
                SizedBox(width: 10), // Additional spacing if needed
              ],
            ),
          ),
        ],
      ),
      drawer: CustomDrawer(),
      body: Column(
        children: [
          Container(
            height: 100, // Adjust the banner height as needed
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/img/internal.jpg'), // Replace with your image asset
                fit: BoxFit.cover
              ),
            ),
            child: Stack(
              children: [
                Container(
                  color: Colors.black.withOpacity(0.5), // Darkening overlay
                ),
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Activités internes', // Replace with your banner text
                          textAlign: TextAlign.start,
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 20.0,
                              fontWeight: FontWeight.bold
                          ),
                        ),
                        Text(
                          'Activités proposées dans votre établissement', // Replace with your banner text
                          textAlign: TextAlign.start,
                          style: TextStyle(
                              color: Colors.grey,
                              fontSize: 15.0
                          ),
                        )
                      ]
                    )
                  ),
                ),
              ],
            ),
          ),
          if (internalEvents != null)
          Expanded(
              child: ListView.builder(
                itemCount: internalEvents!.length,
                itemBuilder: (context, index) {
                  final internalEvent = internalEvents![index];
                  return GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => InternalEventDetailsPage(internalEvent: internalEvent, internalEventStatus: internalEventStatuses[internalEvent.idinternalevent]!, internalEvents: internalEvents!),
                        ),
                      );
                    },
                    child: InternalEventItem(
                      title: internalEvent.titleinternalevent,
                      subtitle: internalEvent.summaryinternalevent,
                      imageUrl: internalEvent.picinternalevent,
                      nextDate: internalEvent.nextdateinternalevent,
                      maxUsers: internalEvent.maxusersinternalevent,
                      status: internalEventStatuses[internalEvent.idinternalevent],
                    )
                  );
                },
              )
          )
        ],
      ),
    );
  }


}