import 'dart:core';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:thermassist_mobile/models/sirtaqui-event.model.dart';
import 'package:thermassist_mobile/parts/drawer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/sirtaqui-communication.model.dart';


class SirtaquiEventDetailsPage extends StatefulWidget {
  final SirtaquiEvent sirtaquiEvent;

  SirtaquiEventDetailsPage({required this.sirtaquiEvent});

  @override
  _SirtaquiEventDetailsPageState createState() => _SirtaquiEventDetailsPageState();
}

class _SirtaquiEventDetailsPageState extends State<SirtaquiEventDetailsPage> {

  @override
  initState() {
    super.initState();
  }

  String generateDateString(SirtaquiEvent sirtaquiEvent) {

    String? start;
    String? end;

    if (sirtaquiEvent.VALIDITEDATESs != null && sirtaquiEvent.OUVERTUREs == null) {
      start = sirtaquiEvent.VALIDITEDATESs![0].datededebut;
      end = sirtaquiEvent.VALIDITEDATESs![0].datedefin;
    } else if (sirtaquiEvent.VALIDITEDATESs == null && sirtaquiEvent.OUVERTUREs != null) {
      start = sirtaquiEvent.OUVERTUREs![0].datedebut;
      end = sirtaquiEvent.OUVERTUREs![0].datefin;
    }

    final now = DateTime.now();
    final dateFormat = DateFormat.yMMMMd('fr');

    if (start != null && end != null) {
      final startDate = DateTime.tryParse(start);
      final endDate = DateTime.tryParse(end);

      if (startDate != null && endDate != null) {
        if (now.isAfter(startDate) && now.isBefore(endDate)) {
          return 'Du ${dateFormat.format(startDate)} au ${dateFormat.format(endDate)}';
        } else {
          return 'Ouverture : non communiquée';
        }
      }
    }

    if (start != null && end == null) {
      final startDate = DateTime.tryParse(start);

      if (startDate != null && now.isAfter(startDate)) {
        return 'A partir du ${dateFormat.format(startDate)}';
      } else {
        return 'Ouverture : non communiquée';
      }
    }

    return 'Ouverture : non communiquée';
  }

  String getType(String input) {
    if (input.contains('https://') || input.contains('http://')) {
      return 'URL';
    } else if (input.contains('@')) {
      return 'EMAIL';
    } else if (input.contains('+')) {
      return 'PHONE';
    } else {
      return 'OTHER';
    }
  }

  String? getWebsite(List<SirtaquiCommunication> communication) {
    String? website;
    communication.forEach((comm) {
      if (getType(comm.coordonneesTelecom!) == 'URL') {
        website = comm.coordonneesTelecom!;
      }
    });
    return website;
  }

  _launchCaller(String phoneNumber) async {
    String url = "tel:$phoneNumber";
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  _launchMail(String email) async {
    String url = "mailto:$email";
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton.icon(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
              foregroundColor: MaterialStateProperty.all(Colors.white),
            ),
            label: Text('Retour'),
          ),
          if (widget.sirtaquiEvent.MOYENSCOMs != null)
          Visibility(
            visible: getWebsite(widget.sirtaquiEvent.MOYENSCOMs!) != null,
            child: ElevatedButton.icon(
              icon: Icon(Icons.link),
              onPressed: () {
                launch(getWebsite(widget.sirtaquiEvent.MOYENSCOMs!)!);
              },
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
                foregroundColor: MaterialStateProperty.all(Colors.white),
              ),
              label: Text('Plus d\'informations'),
            ),
          ),
          if (widget.sirtaquiEvent.PLAQUETTESTRACESs != null)
          Visibility(
            visible: widget.sirtaquiEvent.PLAQUETTESTRACESs != null && widget.sirtaquiEvent.PLAQUETTESTRACESs![0].fichePDFFR != null,
            child: ElevatedButton.icon(
              icon: Icon(Icons.map),
              onPressed: () {
                launch(widget.sirtaquiEvent.PLAQUETTESTRACESs![0].fichePDFFR!.url!);
              },
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
                foregroundColor: MaterialStateProperty.all(Colors.white),
              ),
              label: Text('Plaquette itinéraire'),
            ),
          ),
        ],
      ),
      appBar: AppBar(
        title: Text(widget.sirtaquiEvent.SyndicObjectName!,
          style: const TextStyle(
            color: Colors.white
          )
        ),
        iconTheme: IconThemeData(
          color: Colors.white
        ),
        backgroundColor: Color(0xff4e51ec),
      ),
      drawer: CustomDrawer(),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              height: 100, // Adjust the banner height as needed
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: NetworkImage(widget.sirtaquiEvent.PHOTOSs == null ? "https://images.prismic.io/peopleforbikes/e4073026-1530-40d8-a2d8-6d2b88ff2874_BB_0504.png?auto=compress,format" : widget.sirtaquiEvent.PHOTOSs![0].photo.url!), // Replace with your image asset
                    fit: BoxFit.cover
                ),
              ),
              child: Stack(
                children: [
                  Container(
                    color: Colors.black.withOpacity(0.5), // Darkening overlay
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.sirtaquiEvent.SyndicObjectName!,
                                textAlign: TextAlign.start,
                                style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.bold
                                ),
                              ),
                              Text(
                                generateDateString(widget.sirtaquiEvent), // Replace with your banner text
                                textAlign: TextAlign.start,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 15.0
                                ),
                              ),
                            ]
                        )
                    ),
                  ),
                ],
              ),
            ),
            Padding(
                padding: EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.sirtaquiEvent.DESCRIPTIFACTIVITES == null && widget.sirtaquiEvent.DESCRIPTIFGEN == null ?
                      (widget.sirtaquiEvent.DESCRIPTIFSs != null ? widget.sirtaquiEvent.DESCRIPTIFSs![0].descriptioncommerciale! : "Aucune description n'est disponible pour cette activité.") :
                      (widget.sirtaquiEvent.DESCRIPTIFGEN != null ? widget.sirtaquiEvent.DESCRIPTIFGEN! : widget.sirtaquiEvent.DESCRIPTIFACTIVITES!),
                    ),
                    if (widget.sirtaquiEvent.ADRESSEs != null)
                      SizedBox(height: 16.0),
                    if (widget.sirtaquiEvent.ADRESSEs != null)
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 0.0),
                        child: Text(
                          'Y aller',
                          style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
                        ),
                      ),
                    if (widget.sirtaquiEvent.ADRESSEs != null)
                      SizedBox(height: 12.0),
                    if (widget.sirtaquiEvent.ADRESSEs != null)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          InkWell(
                            onTap: () {
                              launch("https://modalis.fr");
                            },
                            child: Icon(Icons.directions_bus, size: 36.0),
                          ),
                          InkWell(
                            onTap: () {
                              launch("https://modalis.fr");
                            },
                            child: Icon(Icons.train, size: 36.0),
                          ),
                          InkWell(
                            onTap: () {
                              launch("https://maps.google.com/?q=" + widget.sirtaquiEvent.ADRESSEs![0].raisonsociale! + ',' + widget.sirtaquiEvent.ADRESSEs![0].adresse2! + ',' + widget.sirtaquiEvent.ADRESSEs![0].codePostal! + ',' + widget.sirtaquiEvent.ADRESSEs![0].commune!);
                            },
                            child: Icon(Icons.directions_car, size: 36.0),
                          ),
                          InkWell(
                            onTap: () {
                              launch("https://maps.google.com/?q=" + widget.sirtaquiEvent.ADRESSEs![0].raisonsociale! + ',' + widget.sirtaquiEvent.ADRESSEs![0].adresse2! + ',' + widget.sirtaquiEvent.ADRESSEs![0].codePostal! + ',' + widget.sirtaquiEvent.ADRESSEs![0].commune!);
                            },
                            child: Icon(Icons.directions_bike, size: 36.0),
                          ),
                          InkWell(
                            onTap: () {
                              launch("https://maps.google.com/?q=" + widget.sirtaquiEvent.ADRESSEs![0].raisonsociale! + ',' + widget.sirtaquiEvent.ADRESSEs![0].adresse2! + ',' + widget.sirtaquiEvent.ADRESSEs![0].codePostal! + ',' + widget.sirtaquiEvent.ADRESSEs![0].commune!);
                            },
                            child: Icon(Icons.directions_walk, size: 36.0),
                          ),
                        ],
                      ),
                    SizedBox(height: 32.0),
                    Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(widget.sirtaquiEvent.ADRESSEs![0].raisonsociale!, style: const TextStyle(fontWeight: FontWeight.bold)),
                            if (widget.sirtaquiEvent.ADRESSEs![0].adresse2 != null)
                            Text(widget.sirtaquiEvent.ADRESSEs![0].adresse2!),
                            Text("${widget.sirtaquiEvent.ADRESSEs![0].codePostal!} ${widget.sirtaquiEvent.ADRESSEs![0].commune!}"),
                          ],
                        )
                    ),
                    if (widget.sirtaquiEvent.MOYENSCOMs != null)
                      SizedBox(height: 16.0),
                    if (widget.sirtaquiEvent.MOYENSCOMs != null)
                      Padding(
                          padding: EdgeInsets.symmetric(horizontal: 8.0),
                          child: SizedBox(
                            height: 200,
                            child: ListView.builder(
                              itemCount: widget.sirtaquiEvent.MOYENSCOMs!.length,
                              itemBuilder: (context, index) {
                                final com = widget.sirtaquiEvent.MOYENSCOMs![index];
                                final type = getType(com.coordonneesTelecom!);

                                Widget contentWidget;

                                switch (type) {
                                  case 'OTHER':
                                    contentWidget = Text(com.coordonneesTelecom!);
                                    break;
                                  case 'URL':
                                    contentWidget = InkWell(
                                      onTap: () {
                                        launch(com.coordonneesTelecom!);
                                      },
                                      child: Text(
                                        com.coordonneesTelecom!,
                                        style: TextStyle(
                                          color: const Color(0xff585bef),
                                          overflow: TextOverflow.ellipsis
                                        ),
                                      ),
                                    );
                                    break;
                                  case 'EMAIL':
                                    contentWidget = InkWell(
                                      onTap: () async {
                                        _launchMail(com.coordonneesTelecom!);
                                      },
                                      child: Text(
                                        com.coordonneesTelecom!,
                                        style: TextStyle(
                                          color: const Color(0xff585bef),
                                          overflow: TextOverflow.ellipsis
                                        ),
                                      ),
                                    );
                                    break;
                                  case 'PHONE':
                                    contentWidget = InkWell(
                                      onTap: () async {
                                        _launchCaller(com.coordonneesTelecom!);
                                      },
                                      child: Text(
                                        com.coordonneesTelecom!,
                                        style: TextStyle(
                                          color: const Color(0xff585bef),
                                          overflow: TextOverflow.ellipsis
                                        ),
                                      ),
                                    );
                                    break;
                                  default:
                                    contentWidget = Text(com.coordonneesTelecom!);
                                }

                                return Row(
                                  children: [
                                    Text('${com.typedaccesTelecom!.thesLibelle} : '),
                                    Flexible(  // Use Flexible or Expanded to allow text wrapping
                                      child: contentWidget,
                                    ),
                                  ],
                                );
                              },
                            ),
                          )
                      )
                  ],
                )
            )
          ],
        ),
      )
    );
  }


}