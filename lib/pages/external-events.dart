import 'dart:convert';
import 'dart:core';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:intl/intl.dart';
import 'package:thermassist_mobile/models/external-event.model.dart';
import 'package:thermassist_mobile/models/feed.model.dart';
import 'package:thermassist_mobile/models/gridster.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-event.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-feed.model.dart';
import 'package:thermassist_mobile/pages/external-event-details.dart';
import 'package:thermassist_mobile/pages/internal-event-details.dart';
import 'package:thermassist_mobile/pages/sirtaqui-event-details.dart';
import 'package:thermassist_mobile/parts/drawer.dart';
import 'package:thermassist_mobile/parts/external-event-item.dart';
import 'package:thermassist_mobile/parts/internal-event-item.dart';
import 'package:thermassist_mobile/parts/sirtaqui-event-item.dart';
import 'package:thermassist_mobile/parts/widgets/announcement-widget.dart';
import 'package:thermassist_mobile/parts/widgets/clock-widget.dart';
import 'package:thermassist_mobile/parts/widgets/days-widget.dart';
import 'package:thermassist_mobile/parts/widgets/did-you-know-widget.dart';
import 'package:thermassist_mobile/parts/widgets/image-widget.dart';
import 'package:thermassist_mobile/parts/widgets/tides-widget.dart';
import 'package:thermassist_mobile/parts/widgets/video-widget.dart';
import 'package:thermassist_mobile/services/external-event.service.dart';
import 'package:thermassist_mobile/services/internal-event.service.dart';
import 'package:thermassist_mobile/services/settings.service.dart';
import 'package:thermassist_mobile/services/sirtaqui.service.dart';

import '../models/internal-event.model.dart';
import '../models/user.model.dart';
import '../parts/widgets/events-widget.dart';
import '../parts/widgets/weather-widget.dart';
import '../services/auth.service.dart';

class ExternalEventsPage extends StatefulWidget {
  @override
  _ExternalEventsPageState createState() => _ExternalEventsPageState();
}

class _ExternalEventsPageState extends State<ExternalEventsPage> {

  List<ExternalEvent>? externalEvents;
  List<SirtaquiEvent>? sirtaquiEvents;
  List<Feed>? tags;
  Feed? selectedTag;
  User? user;

  @override
  initState() {
    super.initState();
    _fetchUserData();
  }

  void _fetchExternalEvents() async {
    final ExternalEventService externalEventService = ExternalEventService();
    final externalEventData = await externalEventService.getCurrentExternalEvents();
    setState(() {
      externalEvents = externalEventData;
    });
  }

  void _fetchAvailableFeeds() async {
    final SirtaquiService sirtaquiService = SirtaquiService();
    final feedsData = await sirtaquiService.getFeeds();
    setState(() {
      tags = feedsData;
      if (user!.feedsuser != null && jsonDecode(user!.feedsuser!).length != 0) {
        _makeSirtaquiFeed(user!.feedsuser!);
      } else {
        selectedTag = feedsData[0];
        _fetchSirtaquiFeed(selectedTag!);
      }
    });
  }

  void _fetchUserData() async {
    try {
      final authService = AuthService();
      final loggedUser = await authService.whoAmI();
      if (loggedUser.rank.coderank == "USER") {
        _fetchExternalEvents();
      }
      _fetchAvailableFeeds();
      setState(() {
        user = loggedUser;
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _makeSirtaquiFeed(String rawFeeds) async {
    setState(() {
      sirtaquiEvents = null;
    });
    List<dynamic> dynamicList = jsonDecode(rawFeeds);
    List<String> feeds = List<String>.from(dynamicList);

    final SirtaquiService sirtaquiService = SirtaquiService();

    List<SirtaquiEvent> matchingZip = [];
    List<SirtaquiEvent> finalEvents = [];

    String area = user!.establishment.areaestablishment!;
    String zip = user!.establishment.station!.zipstation!;

    for (String feed in feeds) {
      Feed? feedMetadata = getFeedById(feed);
      if (feedMetadata == null) continue;
      final feedData = await sirtaquiService.getFeedEvents(feedMetadata.namefeed);
      feedData.value.forEach((event) {
        if (event.ADRESSEs != null && event.MOYENSCOMs != null && event.ZONESs != null) {
          for (var zone in event.ZONESs!) {
            if (zone.zoneLibelle == 'Thermassist - $area') {
              if (event.ADRESSEs![0].codePostal == zip) {
                matchingZip.add(event);
              } else {
                finalEvents.add(event);
              }
            }
          }
        }
      });
    }

    finalEvents.shuffle(Random());

    List<SirtaquiEvent> sortedEvents = matchingZip + finalEvents;

    setState(() {
      sirtaquiEvents = sortedEvents;
    });

  }

  Feed? getFeedById(String feedId) {
    for (int i = 0; i < tags!.length; i++) {
      if (tags![i].idfeed == feedId) {
        return tags![i];
      }
    }
    return null;
  }

  void _fetchSirtaquiFeed(Feed feed) async {
    setState(() {
      sirtaquiEvents = null;
    });
    final SirtaquiService sirtaquiService = SirtaquiService();
    final feedData = await sirtaquiService.getFeedEvents(feed.namefeed);

    List<SirtaquiEvent> matchingZip = [];
    List<SirtaquiEvent> finalEvents = [];

    String area = user!.establishment.areaestablishment!;
    String zip = user!.establishment.station!.zipstation != null ? user!.establishment.station!.zipstation! : "";

    feedData.value.forEach((event) {
      if (event.ADRESSEs != null && event.MOYENSCOMs != null && event.ZONESs != null) {
        for (var zone in event.ZONESs!) {
          if (zone.zoneLibelle == 'Thermassist - $area') {
            if (event.ADRESSEs![0].codePostal == zip) {
              matchingZip.add(event);
            } else {
              finalEvents.add(event);
            }
          }
        }
      }
    });

    List<SirtaquiEvent> sortedEvents = matchingZip + finalEvents;

    setState(() {
      sirtaquiEvents = sortedEvents;
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(length: 2, child:
      Scaffold(
        appBar: AppBar(
          title: const Text('Activités Externes',
              style: TextStyle(
                  color: Colors.white
              )
          ),
          iconTheme: IconThemeData(
              color: Colors.white
          ),
          backgroundColor: Color(0xff4e51ec),
        ),
        drawer: CustomDrawer(),
        body: Column(
          children: [
            Container(
              height: 100, // Adjust the banner height as needed
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage('assets/img/external.jpeg'), // Replace with your image asset
                    fit: BoxFit.cover,
                    alignment: Alignment.topLeft
                ),
              ),
              child: Stack(
                children: [
                  Container(
                    color: Colors.black.withOpacity(0.5), // Darkening overlay
                  ),
                  const Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Activités externes', // Replace with your banner text
                                textAlign: TextAlign.start,
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.bold
                                ),
                              ),
                              Text(
                                'Activités proposées autour de votre établissement', // Replace with your banner text
                                textAlign: TextAlign.start,
                                style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 15.0
                                ),
                              )
                            ]
                        )
                    ),
                  ),
                ],
              ),
            ),
            if (user != null && user?.rank.coderank == "USER" && (externalEvents != null && externalEvents!.isNotEmpty))
            Container(
              height: 60,
              child: TabBar(
                tabs: [
                  Tab(text: "Découverte",),
                  if (user!.establishment.station!.zipstation != null && user!.establishment.station!.zipstation!.isNotEmpty)
                  Tab(text: "Offres établissement"),
                ],
              ),
            ),
            SizedBox(
              height: 8.0
            ),
            if (tags != null)
            Expanded(
              child: Padding(
                padding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 0.0),
                child: TabBarView(
                  children: [
                    // Tab 1 - Discovery
                    if (user!.establishment.station!.zipstation != null && user!.establishment.station!.zipstation!.isNotEmpty)
                    Column(
                      children: [
                        Wrap(
                          spacing: 8.0, // Adjust the spacing between chips as needed
                          runSpacing: 4.0,
                          children: [

                            if (user!.feedsuser != null && jsonDecode(user!.feedsuser!).length != 0)
                            GestureDetector(
                              onTap: () {
                                _makeSirtaquiFeed(user!.feedsuser!);
                                setState(() {
                                  selectedTag = null;
                                });
                              },
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(64.0), // Adjust the border radius as needed
                                    color: selectedTag == null ? const Color(0xff585bef) : const Color(0xffdedede),
                                  ),
                                  padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0), // Adjust padding as needed
                                  child: Text(
                                    "Ma personnalisation",
                                    style: TextStyle(
                                        color: selectedTag == null ? Colors.white : Colors.black,
                                        fontSize: 10
                                    ),
                                  ),
                                )
                            ),

                            ...tags!.map((tag) {
                            bool isSelected = selectedTag == tag;

                            return GestureDetector(
                                onTap: () {
                                  _fetchSirtaquiFeed(tag);
                                  setState(() {
                                    selectedTag = tag;
                                  });
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(64.0), // Adjust the border radius as needed
                                    color: isSelected ? const Color(0xff585bef) : const Color(0xffdedede),
                                  ),
                                  padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0), // Adjust padding as needed
                                  child: Text(
                                    tag.displaynamefeed,
                                    style: TextStyle(
                                        color: isSelected ? Colors.white : Colors.black,
                                        fontSize: 10
                                    ),
                                  ),
                                )
                            );
                          }).toList()]
                        ),
                        SizedBox(
                          height: 12.0
                        ),
                        if (sirtaquiEvents == null)
                        SizedBox(
                          height: 64.0
                        ),
                        if (sirtaquiEvents == null)
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(), // Loading spinner
                              SizedBox(height: 24.0),
                              Text("Nous récupérons vos activités. Ce temps de chargement peut être long selon le nombre d'activités.", textAlign: TextAlign.center, style: TextStyle(fontSize: 12.0),), // Loading message
                            ],
                          ),
                        ),
                        if (sirtaquiEvents != null)
                        Expanded(
                            child:ListView.builder(
                              itemCount: sirtaquiEvents!.length,
                              itemBuilder: (context, index) {
                                final sirtaquiEvent = sirtaquiEvents![index];
                                final description = sirtaquiEvent.DESCRIPTIFACTIVITES == null && sirtaquiEvent.DESCRIPTIFGEN == null ?
                                    (sirtaquiEvent.DESCRIPTIFSs != null ? sirtaquiEvent.DESCRIPTIFSs![0].descriptioncommerciale : "Aucune description n'est disponible pour cette activité.") :
                                    (sirtaquiEvent.DESCRIPTIFGEN != null ? sirtaquiEvent.DESCRIPTIFGEN : sirtaquiEvent.DESCRIPTIFACTIVITES);
                                return GestureDetector(
                                    onTap: () {
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) => SirtaquiEventDetailsPage(sirtaquiEvent: sirtaquiEvent),
                                        ),
                                      );
                                    },
                                    child: SirtaquiEventItem(
                                      title: sirtaquiEvent.SyndicObjectName!,
                                      subtitle: description!,
                                      imageUrl: sirtaquiEvent.PHOTOSs != null ? sirtaquiEvent.PHOTOSs![0].photo.url : null,
                                    )
                                );
                              },
                            )
                        )
                      ],
                    ),
                    // Tab 2 - External events
                    ListView.builder(
                      itemCount: externalEvents != null ? externalEvents!.length : 0,
                      itemBuilder: (context, index) {
                        final externalEvent = externalEvents![index];
                        return GestureDetector(
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => ExternalEventDetailsPage(externalEvent: externalEvent),
                                ),
                              );
                            },
                            child: ExternalEventItem(
                              title: externalEvent.titleexternalevent,
                              subtitle: externalEvent.summaryexternalevent,
                              imageUrl: externalEvent.picexternalevent,
                            )
                        );
                      },
                    )
                  ],
                )
              )
            )
          ],
        ),
      )
    );
  }


}