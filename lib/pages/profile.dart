import 'dart:convert';
import 'dart:core';
import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:intl/intl.dart';
import 'package:thermassist_mobile/models/sirtaqui-event.model.dart';
import 'package:thermassist_mobile/models/user-preferences.model.dart';
import 'package:thermassist_mobile/pages/welcome.dart';
import 'package:thermassist_mobile/parts/drawer.dart';
import 'package:thermassist_mobile/services/user.service.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/feed.model.dart';
import '../models/user.model.dart';
import '../parts/profile-info-item.dart';
import '../services/auth.service.dart';
import '../services/sirtaqui.service.dart';


class ProfilePage extends StatefulWidget {

  const ProfilePage({super.key});

  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {

  User? user;

  bool emailCommunication = true;
  bool smsCommunication = true;
  List<String> feeds = [];

  List<Feed>? tags;


  TextEditingController deleteAccountPasswordController = TextEditingController();

  TextEditingController changePasswordCurrentPasswordController = TextEditingController();
  TextEditingController changePasswordNewPasswordController = TextEditingController();
  TextEditingController changePasswordNewPasswordValidationController = TextEditingController();

  // Password visibility state variables
  bool _deleteAccountPasswordVisible = false;
  bool _currentPasswordVisible = false;
  bool _newPasswordVisible = false;
  bool _confirmPasswordVisible = false;

  @override
  initState() {
    super.initState();
    _fetchUserData();
  }

  bool _isFeedSelected(Feed feed) {
    return feeds.contains(feed.idfeed);
  }

  void _updatePreferences() async {
    try {
      final userService = UserService();
      await userService.updatePreferences(UserPreferences(sms: smsCommunication, email: emailCommunication));
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _updateFeeds() async {
    try {
      final userService = UserService();
      await userService.updateFeeds(feeds);
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _fetchAvailableFeeds() async {
    final SirtaquiService sirtaquiService = SirtaquiService();
    final feedsData = await sirtaquiService.getFeeds();
    setState(() {
      tags = feedsData;
    });
  }

  void _fetchUserData() async {
    try {
      final authService = AuthService();
      final loggedUser = await authService.whoAmI();
      setState(() {
        user = loggedUser;
        if (loggedUser.preferencesuser != null) {
          emailCommunication = loggedUser.preferencesuser!.email;
          smsCommunication = loggedUser.preferencesuser!.sms;
        }
        if (loggedUser.feedsuser != null) {
          List<dynamic> stringList = jsonDecode(loggedUser.feedsuser!);
          feeds = stringList.cast<String>().toList();
        }
        _fetchAvailableFeeds();
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _showDeleteConfirmation(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              padding: EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
              Text(
                'Supprimer mon compte',
                style: TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16.0),
              Text(
                'Êtes-vous sûr de vouloir supprimer votre compte ? Vous perdrez toutes les données liées à votre profil. Vous pouvez demander à votre établissement de vous recréer un compte si nécessaire.',
                style: TextStyle(fontSize: 16.0),
              ),
              SizedBox(height: 16.0),
              Text(
                'Afin de valider la suppression, veuillez entrer votre mot de passe.',
                style: TextStyle(fontSize: 16.0),
              ),
              SizedBox(height: 16.0),
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                  topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                ),
                child: Container(
                  color: Colors.grey[200], // Set the background color for the password field
                  child: TextFormField(
                    controller: deleteAccountPasswordController,
                    decoration: InputDecoration(
                      labelText: 'Mot de passe',
                      contentPadding: EdgeInsets.all(8.0),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _deleteAccountPasswordVisible ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setModalState(() {
                            _deleteAccountPasswordVisible = !_deleteAccountPasswordVisible;
                          });
                        },
                      ),
                    ),
                    obscureText: !_deleteAccountPasswordVisible,
                  ),
                ),
              ),
              SizedBox(height: 16.0),
              Container(
                width: double.infinity, // Make the button take the full width
                child: TextButton(
                  onPressed: () => _deleteAccount(),
                  style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(const Color(
                        0xffe7562e)),
                    foregroundColor: MaterialStateProperty.all(Colors.white),
                    padding: MaterialStateProperty.all(
                      EdgeInsets.symmetric(vertical: 8.0),
                    ),
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32.0), // Change the border radius
                      ),
                    ),
                  ),
                  child: Text(
                    'Supprimer mon compte',
                    style: TextStyle(fontSize: 18.0),
                  ),
                ),
              ),
            ],
          ),
        );
          },
        );
      },
    );
  }

  void _showPasswordChangeConfirmation(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              padding: EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
              Text(
                'Changer mon mot de passe',
                style: TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16.0),
              Text(
                'Vous pouvez modifier votre mot de passe en remplissant les champs ci-dessous. Votre mot de passe sera immédiatement effectif dès sa validation.',
                style: TextStyle(fontSize: 16.0),
              ),
              SizedBox(height: 16.0),
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                  topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                ),
                child: Container(
                  color: Colors.grey[200], // Set the background color for the password field
                  child: TextFormField(
                    controller: changePasswordCurrentPasswordController,
                    decoration: InputDecoration(
                      labelText: 'Mot de passe actuel',
                      contentPadding: EdgeInsets.all(8.0),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _currentPasswordVisible ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setModalState(() {
                            _currentPasswordVisible = !_currentPasswordVisible;
                          });
                        },
                      ),
                    ),
                    obscureText: !_currentPasswordVisible,
                  ),
                ),
              ),
              SizedBox(height: 16.0),
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                  topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                ),
                child: Container(
                  color: Colors.grey[200], // Set the background color for the password field
                  child: TextFormField(
                    controller: changePasswordNewPasswordController,
                    decoration: InputDecoration(
                      labelText: 'Nouveau mot de passe',
                      contentPadding: EdgeInsets.all(8.0),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _newPasswordVisible ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setModalState(() {
                            _newPasswordVisible = !_newPasswordVisible;
                          });
                        },
                      ),
                    ),
                    obscureText: !_newPasswordVisible,
                  ),
                ),
              ),
              SizedBox(height: 16.0),
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                  topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                ),
                child: Container(
                  color: Colors.grey[200], // Set the background color for the password field
                  child: TextFormField(
                    controller: changePasswordNewPasswordValidationController,
                    decoration: InputDecoration(
                      labelText: 'Confirmation du nouveau mot de passe',
                      contentPadding: EdgeInsets.all(8.0),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _confirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setModalState(() {
                            _confirmPasswordVisible = !_confirmPasswordVisible;
                          });
                        },
                      ),
                    ),
                    obscureText: !_confirmPasswordVisible,
                  ),
                ),
              ),
              SizedBox(height: 16.0),
              Container(
                width: double.infinity, // Make the button take the full width
                child: TextButton(
                  onPressed: () => _updatePassword(),
                  style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(const Color(
                        0xff4e51ec)),
                    foregroundColor: MaterialStateProperty.all(Colors.white),
                    padding: MaterialStateProperty.all(
                      EdgeInsets.symmetric(vertical: 8.0),
                    ),
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32.0), // Change the border radius
                      ),
                    ),
                  ),
                  child: Text(
                    'Changer mon mot de passe',
                    style: TextStyle(fontSize: 18.0),
                  ),
                ),
              ),
            ],
          ),
        );
          },
        );
      },
    );
  }

  void _updatePassword() async {
    if (changePasswordNewPasswordController.text.trim() != changePasswordNewPasswordValidationController.text.trim()) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Nouveau mot de passe non valide'),
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            content: Text('La validation de votre nouveau mot de passe ne correspond pas à celui entré.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('OK'),
              ),
            ],
          );
        },
      );
      return;
    }
    try {
      final userService = UserService();
      try {
        await userService.updatePassword(changePasswordCurrentPasswordController.text.trim(), changePasswordNewPasswordController.text.trim());
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Mot de passe modifié'),
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              content: Text('Votre mot de passe a bien été modifié.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('OK'),
                ),
              ],
            );
          },
        );
      } catch (e) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Modification impossible'),
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              content: Text('Votre mot de passe est incorrect.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _deleteAccount() async {
    try {
      final userService = UserService();
      try {
        await userService.deleteAccount(deleteAccountPasswordController.text.trim());
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Compte supprimé'),
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              content: Text('Votre compte a bien été supprimé. Vous allez être redirigé vers l\'accueil.'),
              actions: [
                TextButton(
                  onPressed: _disconnect,
                  child: Text('OK'),
                ),
              ],
            );
          },
        );
      } catch (e) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Suppression impossible'),
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              content: Text('Votre mot de passe est incorrect.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _disconnect() async {
    final storage = new FlutterSecureStorage();
    await storage.delete(key: 'THERMASSIST_TOKEN');
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => WelcomePage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        length: 1,
        child: Scaffold(
          appBar: AppBar(
            title: Text("Mon profil",
                style: const TextStyle(
                    color: Colors.white
                )
            ),
            iconTheme: IconThemeData(
                color: Colors.white
            ),
            backgroundColor: Color(0xff4e51ec),
          ),
          drawer: CustomDrawer(),
          body: SingleChildScrollView(
            child: Column(
              children: [
                Stack(
                  children: [
                    Column(
                      children: [
                        Stack(
                          children: [
                            Container(
                              height: 150, // Adjust the height as needed
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage('assets/img/profile.jpg'), // Replace with your image
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.3), // Adjust opacity as needed
                                ),
                              ),
                            ),
                          ],
                        ),
                        Container(
                          height: 60,
                          child: TabBar(
                            tabs: [
                              Tab(text: "A propos",),
                            ],
                          ),
                        ),
                      ],
                    ),
                    if (user != null)
                    Positioned(
                      left: 16.0, // Adjust as needed
                      bottom: 16.0, // Adjust as needed
                      child: CircleAvatar(
                        radius: 50.0, // Adjust as needed
                        backgroundImage: NetworkImage("https://ui-avatars.com/api/?background=0D8ABC&color=fff&name=" + user!.prenomuser + user!.nomuser + "&size=128"), // Replace with user profile image
                      ),
                    ),
                    if (user != null)
                    Positioned(
                      left: 128.0, // Adjust as needed
                      bottom: 70.0, // Adjust as needed
                      child: Text(user!.prenomuser + " " + user!.nomuser, style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 22.0),),
                    )
                  ],
                ),
                Container(
                  width: double.infinity,
                  child: Card(
                    surfaceTintColor: Colors.white,
                    margin: EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 8.0),
                        Padding(
                          padding: const EdgeInsets.only(left: 16.0),
                          child: Text(
                            'A propos', // Replace with your desired title
                            style: TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Divider(),
                        Padding(
                          padding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                          child: Column(
                            children: [
                              if (user != null)
                                ProfileInfoItem(
                                  icon: Icons.apartment, // Replace with the appropriate icon
                                  title: 'Etablissement',
                                  value: user!.establishment.nameestablishment, // Replace with the actual user data
                                ),
                              if (user?.emailuser != null)
                                ProfileInfoItem(
                                  icon: Icons.email, // Replace with the appropriate icon
                                  title: 'Email',
                                  value: user!.emailuser, // Replace with the actual user data
                                ),
                              if (user?.phoneuser != null)
                                ProfileInfoItem(
                                  icon: Icons.phone, // Replace with the appropriate icon
                                  title: 'Mobile',
                                  value: user!.phoneuser!, // Replace with the actual user data
                                ),
                              if (user?.startstayuser != null && user?.endstayuser != null)
                                ProfileInfoItem(
                                  icon: Icons.today, // Replace with the appropriate icon
                                  title: 'Séjour',
                                  value: DateFormat('dd MMM yyyy').format(DateTime.parse(user!.startstayuser!)) + " - " + DateFormat('dd MMM yyyy').format(DateTime.parse(user!.endstayuser!)), // Replace with the actual user data
                                ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Container(
                  width: double.infinity,
                  child: Card(
                    surfaceTintColor: Colors.white,
                    margin: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 8.0),
                        Padding(
                          padding: const EdgeInsets.only(left: 16.0),
                          child: Text(
                            'Paramètres de communication', // Replace with your desired title
                            style: TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Divider(),
                        Padding(
                          padding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Refuser les emails',
                                    style: TextStyle(fontSize: 14.0),
                                  ),
                                  Transform.scale(
                                    scale: 0.8, // Adjust the scale factor to make the switch smaller
                                    child: Switch(
                                      value: !emailCommunication, // Replace with your actual value
                                      onChanged: (value) {
                                        setState(() {
                                          emailCommunication = !emailCommunication;
                                          _updatePreferences();
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Refuser les SMS',
                                    style: TextStyle(fontSize: 14.0),
                                  ),
                                  Transform.scale(
                                    scale: 0.8, // Adjust the scale factor to make the switch smaller
                                    child: Switch(
                                      value: !smsCommunication, // Replace with your actual value
                                      onChanged: (value) {
                                        setState(() {
                                          smsCommunication = !smsCommunication;
                                          _updatePreferences();
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Container(
                  width: double.infinity,
                  child: Card(
                    surfaceTintColor: Colors.white,
                    margin: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 8.0),
                        Padding(
                          padding: const EdgeInsets.only(left: 16.0),
                          child: Text(
                            'Mon profil curiste', // Replace with your desired title
                            style: TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Divider(),
                        if (tags != null)
                          Padding(
                            padding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                            child: Column(
                              children: [
                                Wrap(
                                    spacing: 8.0, // Adjust the spacing between chips as needed
                                    runSpacing: 4.0,
                                    children: [
                                      ...tags!.map((tag) {
                                        bool isSelected = _isFeedSelected(tag);

                                        return GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                if (feeds.contains(tag.idfeed)) {
                                                  feeds.remove(tag.idfeed);
                                                } else {
                                                  feeds.add(tag.idfeed);
                                                }
                                                _updateFeeds();
                                              });
                                            },
                                            child: Container(
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(64.0), // Adjust the border radius as needed
                                                color: isSelected ? const Color(0xff585bef) : const Color(0xffdedede),
                                              ),
                                              padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0), // Adjust padding as needed
                                              child: Text(
                                                tag.displaynamefeed,
                                                style: TextStyle(
                                                    color: isSelected ? Colors.white : Colors.black,
                                                    fontSize: 10
                                                ),
                                              ),
                                            )
                                        );
                                      }).toList()]
                                ),
                              ],
                            ),
                          )
                      ],
                    ),
                  ),
                ),
                Container(
                  width: double.infinity,
                  child: Card(
                    surfaceTintColor: Colors.white,
                    margin: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 8.0),
                        Padding(
                          padding: const EdgeInsets.only(left: 16.0),
                          child: Text(
                            'Gestion du compte', // Replace with your desired title
                            style: TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Divider(),
                        Padding(
                            padding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                            child: Column(
                              children: [
                                Container(
                                  width: double.infinity, // Make the button take the full width
                                  child: TextButton(
                                    onPressed: () => _showPasswordChangeConfirmation(context),
                                    style: ButtonStyle(
                                      backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
                                      foregroundColor: MaterialStateProperty.all(Colors.white),
                                      padding: MaterialStateProperty.all(
                                        EdgeInsets.symmetric(vertical: 8.0),
                                      ),
                                      shape: MaterialStateProperty.all(
                                        RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(32.0), // Change the border radius
                                        ),
                                      ),
                                    ),
                                    child: Text(
                                      'Modifier mon mot de passe',
                                      style: TextStyle(fontSize: 18.0),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 8.0), // Add some spacing between buttons
                                Container(
                                  width: double.infinity, // Make the button take the full width
                                  child: TextButton(
                                    onPressed: () => _showDeleteConfirmation(context),
                                    style: ButtonStyle(
                                      backgroundColor: MaterialStateProperty.all(const Color(
                                          0xffe7562e)),
                                      foregroundColor: MaterialStateProperty.all(Colors.white),
                                      padding: MaterialStateProperty.all(
                                        EdgeInsets.symmetric(vertical: 8.0),
                                      ),
                                      shape: MaterialStateProperty.all(
                                        RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(32.0), // Change the border radius
                                        ),
                                      ),
                                    ),
                                    child: Text(
                                      'Supprimer mon compte',
                                      style: TextStyle(fontSize: 18.0),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )
        )
    );
  }


}