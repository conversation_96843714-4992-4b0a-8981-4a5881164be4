import 'dart:convert';
import 'dart:core';

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:thermassist_mobile/models/basket-item.model.dart';
import 'package:thermassist_mobile/parts/drawer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../config/config.dart';
import '../models/internal-event-price.model.dart';
import '../models/internal-event-status.model.dart';
import '../models/internal-event.model.dart';
import '../models/service-config.model.dart';
import '../services/reservation.service.dart';
import '../services/settings.service.dart';
import '../storage/basket.storage.dart';

class InternalEventDetailsPage extends StatefulWidget {
  final InternalEvent internalEvent;
  final List<InternalEvent> internalEvents;
  final InternalEventStatus internalEventStatus;

  InternalEventDetailsPage({required this.internalEvent, required this.internalEventStatus, required this.internalEvents});

  @override
  _InternalEventDetailsPageState createState() => _InternalEventDetailsPageState();
}

class _InternalEventDetailsPageState extends State<InternalEventDetailsPage> {

  List<InternalEventPrice>? prices;
  List<ServiceConfig>? serviceSettings;
  int? selectedPriceIndex = 0;

  @override
  initState() {
    super.initState();
    _fetchSettings();
    if (widget.internalEvent.pricesinternalevent != null) {
      prices = parsePrices(widget.internalEvent.pricesinternalevent!);
    }
  }

  void _fetchSettings() async {
    try {
      final settingsService = SettingsService();
      final settingsData = await settingsService.getCurrentSettings();
      setState(() {
        if (settingsData.servicessettings != null) {
          serviceSettings = (jsonDecode(settingsData.servicessettings) as List)
              .map((e) => ServiceConfig.fromJson(e)).toList();
        }
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  bool _canDisplay(String key) {
    if (serviceSettings == null) return true;
    for (var service in serviceSettings!) {
      print(service.name);
      if (service.name == key) {
        return service.status!;
      }
    }
    // If the service with the given ID is not found, return true by default
    return true;
  }

  void _showPriceSelectionModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Choisissez un tarif"),
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setModalState) {
              return DropdownButton<int>(
                isExpanded: true,
                value: selectedPriceIndex,
                items: prices?.asMap().entries.map((entry) {
                  int index = entry.key;
                  InternalEventPrice price = entry.value;
                  return DropdownMenuItem<int>(
                    value: index,
                    child: Text('${price.label} - ${price.price}€'),
                  );
                }).toList(),
                onChanged: (int? newIndex) {
                  setModalState(() {
                    selectedPriceIndex = newIndex;
                  });
                },
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: selectedPriceIndex != null
                  ? () async {
                if (widget.internalEvent.paymentmodeinternalevent == "ONLINE") {
                  addToCart(widget.internalEvent, selectedPriceIndex!);
                } else {
                  final ReservationService reservationService = ReservationService();
                  await reservationService.registerReservationOnSite(widget.internalEvent.idinternalevent, selectedPriceIndex!);
                  Fluttertoast.showToast(
                      msg: "Votre réservation a bien été enregistrée.",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.BOTTOM,
                      timeInSecForIosWeb: 2,
                      backgroundColor: Colors.white,
                      textColor: Colors.black,
                      fontSize: 16.0
                  );
                }
                Navigator.of(context).pop();
              }
                  : null,
              child: Text("Ajouter au panier"),
              style: TextButton.styleFrom(
                backgroundColor: selectedPriceIndex != null ? const Color(0xff585bef) : Colors.grey,
              ),
            ),

            // "Voir mon panier" button
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close modal
                _showBasketBottomSheet(context); // Trigger the basket bottom sheet
              },
              child: const Text(
                "Voir mon panier",
                style: TextStyle(
                  color: Color(0xff585bef), // Color for "Voir mon panier"
                  fontSize: 12.0, // Smaller font size
                ),
              ),
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero, // No padding for the smaller button
                minimumSize: Size(50, 30), // Make the button smaller
                tapTargetSize: MaterialTapTargetSize.shrinkWrap, // Shrink the touch target
              ),
            ),
          ],
        );
      },
    );
  }

  void addToCart(InternalEvent internalEvent, int priceIndex) async {
    List<BasketItem> currentItems = await BasketStorage.loadBasketItems();
    BasketItem newItem = BasketItem(internalEventId: internalEvent.idinternalevent, pricingId: priceIndex);
    currentItems.add(newItem);
    await BasketStorage.saveBasketItems(currentItems);

    print('Added to cart: ${internalEvent.idinternalevent}, Price index: $priceIndex');
  }

  List<InternalEventPrice> parsePrices(String pricesJson) {
    print(pricesJson);
    List<dynamic> jsonList = jsonDecode(pricesJson);
    return jsonList.map((json) => InternalEventPrice.fromJson(json)).toList();
  }

  String formatNextDate(String nextDate) {
    // Parse the string into a DateTime object
    DateTime parsedDate = DateTime.parse(nextDate);

    // Format the DateTime object into the desired format
    String formattedDate = "${DateFormat('dd/MM/yyyy à HH:mm').format(parsedDate)}h";

    return formattedDate;
  }

  String formatLimitDate(String nextDate, int daysBefore) {
    // Parse the string into a DateTime object
    DateTime parsedDate = DateTime.parse(nextDate);
    DateTime newDate = DateTime(parsedDate.year, parsedDate.month, parsedDate.day - daysBefore);

    // Format the DateTime object into the desired format
    String formattedDate = DateFormat('dd/MM/yyyy').format(newDate);

    return formattedDate;
  }

  String generateRepeatSentence() {
    if (widget.internalEvent.triggersinternalevent == null) {
      return 'Aucune donnée disponible.';
    }

    // Parse JSON data
    final triggersData = jsonDecode(widget.internalEvent.triggersinternalevent!);

    switch (widget.internalEvent.frequencyinternalevent) {
      case 'DAILY':
        return 'A lieu quotidiennement, à ${triggersData['time']}h.';

      case 'WEEKLY':
        return 'A lieu de manière hebdomadaire, le ${createReadableDayName(triggersData['day'])} à ${triggersData['time']}h.';

      case 'WEEKLY-MULTI':
        return 'A lieu de manière hebdomadaire, le ${createCompoundDays(triggersData['days'])} à ${triggersData['time']}h.';

      case 'MONTHLY':
        return 'A lieu mensuellement, le ${triggersData['day']} du mois à ${triggersData['time']}h.';

      case 'YEARLY':
        return 'A lieu annuellement, le ${triggersData['day']} ${createReadableMonthName(triggersData['month'])} à ${triggersData['time']}h.';

      default:
        return 'Fréquence non définie.';
    }
  }

  String createCompoundDays(List<String> days) {
    String result = '';
    for (var day in days) {
      result += '${createReadableDayName(day)}, ';
    }
    // Remove the trailing comma and space
    if (result.isNotEmpty) {
      result = result.substring(0, result.length - 2);
    }
    return result;
  }

  String createReadableDayName(String day) {
    switch (day) {
      case 'MONDAY':
        return 'Lundi';
      case 'TUESDAY':
        return 'Mardi';
      case 'WEDNESDAY':
        return 'Mercredi';
      case 'THURSDAY':
        return 'Jeudi';
      case 'FRIDAY':
        return 'Vendredi';
      case 'SATURDAY':
        return 'Samedi';
      case 'SUNDAY':
        return 'Dimanche';
      default:
        return 'Jour inconnu'; // Handle unexpected day values
    }
  }

  String createReadableMonthName(String month) {
    switch (month) {
      case 'JANUARY':
        return 'Janvier';
      case 'FEBRUARY':
        return 'Février';
      case 'MARCH':
        return 'Mars';
      case 'APRIL':
        return 'Avril';
      case 'MAY':
        return 'Mai';
      case 'JUNE':
        return 'Juin';
      case 'JULY':
        return 'Juillet';
      case 'AUGUST':
        return 'Août';
      case 'SEPTEMBER':
        return 'Septembre';
      case 'OCTOBER':
        return 'Octobre';
      case 'NOVEMBER':
        return 'Novembre';
      case 'DECEMBER':
        return 'Décembre';
      default:
        return 'Mois inconnu'; // Handle unexpected month values
    }
  }

  Future<void> _showBasketBottomSheet(BuildContext context) async {
    List<BasketItem> basketItems = await BasketStorage.loadBasketItems();

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      constraints: const BoxConstraints(
        maxWidth: double.infinity,
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return SafeArea(
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height / 2,
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Panier',
                                style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                'Total: ' + _calculateTotalPrice(basketItems).toString() + '€',
                                style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 16.0),
                        if (basketItems.isEmpty)
                          Expanded(
                            child: Center(
                              child: Text(
                                'Vous n\'avez aucun article dans votre panier.',
                                style: TextStyle(fontSize: 16.0, color: Colors.grey),
                              ),
                            ),
                          ),
                        if (basketItems.isNotEmpty)
                          Expanded(
                            child: ListView.builder(
                              itemCount: basketItems.length,
                              itemBuilder: (context, index) {
                                BasketItem item = basketItems[index];
                                InternalEvent? event = findInternalEventById(item.internalEventId);
                                return ListTile(
                                  title: Text(event!.titleinternalevent),
                                  subtitle: Text(getPriceByIndex(item.internalEventId, item.pricingId).toString() + '€'),
                                  trailing: IconButton(
                                    icon: Icon(Icons.delete, color: Colors.black54),
                                    onPressed: () {
                                      setState(() {
                                        basketItems.removeAt(index);
                                      });
                                      _removeItemFromBasket(index, context);
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: ElevatedButton(
                            onPressed: basketItems.isNotEmpty ? () async {
                              final ReservationService reservationService = ReservationService();
                              final checkoutLink = await reservationService.payForReservation(basketItems);
                              BasketStorage.clearBasket();
                              _openBrowser(checkoutLink);
                              Navigator.pop(context); // Close the bottom sheet after checkout
                            } : null,
                            child: Text('Passer au paiement'),
                            style: ElevatedButton.styleFrom(
                                minimumSize: Size(double.infinity, 50),
                                backgroundColor: const Color(0xff585bef),
                                foregroundColor: Colors.white
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }
        );
      },
    );
  }

  void _openBrowser(String url) async {
    print(url);
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  Future<void> _removeItemFromBasket(int index, BuildContext context) async {
    List<BasketItem> basketItems = await BasketStorage.loadBasketItems();
    basketItems.removeAt(index);
    await BasketStorage.saveBasketItems(basketItems);
  }

  double _calculateTotalPrice(List<BasketItem> basketItems) {
    // Implement your logic to calculate total price based on items in the basket
    double totalPrice = 0.0;
    for (BasketItem item in basketItems) {
      // Assume you have a method to get price by index
      totalPrice += getPriceByIndex(item.internalEventId, item.pricingId);
    }
    return totalPrice;
  }

  double getPriceByIndex(int internalEventId, int index) {
    InternalEvent? event = findInternalEventById(internalEventId);
    if (event == null) return 0.0;
    List<InternalEventPrice> prices = parsePrices(event!.pricesinternalevent!);
    return prices[index].price!; // Dummy price
  }

  InternalEvent? findInternalEventById(int id) {
    for (InternalEvent event in widget.internalEvents) {
      if (event.idinternalevent == id) return event;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton.icon(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
              foregroundColor: MaterialStateProperty.all(Colors.white),
            ),
            label: Text('Retour'),
          ),
          const SizedBox(
              width: 8
          ),
          if (widget.internalEvent.nextdateinternalevent != null && _canDisplay("Paiement en ligne"))
          ElevatedButton.icon(
            icon: Icon(Icons.add_shopping_cart),
            onPressed: !widget.internalEventStatus.registered ? () async {
              _showPriceSelectionModal(context);
            } : null,
            style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xff585bef),
                foregroundColor: Colors.white
            ),
            label: Text(widget.internalEventStatus.registered ? 'Vous êtes déjà inscrit.' : (widget.internalEvent.paymentmodeinternalevent == "ONLINE" ? 'Ajouter au panier' : 'Réserver et payer sur place')),
          ),
          const SizedBox(
            width: 8
          ),
          Visibility(
            visible: widget.internalEvent.linkinternalevent != null,
            child: ElevatedButton.icon(
              icon: Icon(Icons.link),
              onPressed: () {
                launch(widget.internalEvent.linkinternalevent!);
              },
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
                foregroundColor: MaterialStateProperty.all(Colors.white),
              ),
              label: Text('Plus d\'informations'),
            ),
          )
        ],
      ),
      appBar: AppBar(
        title: Text(widget.internalEvent.titleinternalevent,
          style: const TextStyle(
            color: Colors.white
          )
        ),
        actions: [
          if (_canDisplay("Paiement en ligne"))
          GestureDetector(
            onTap: () {
              _showBasketBottomSheet(context); // Trigger the bottom sheet
            },
            child: const Row(
              children: [
                Icon(Icons.shopping_basket, color: Colors.white),
                SizedBox(width: 5), // Add spacing between the icon and text
                Text(
                  'Panier',
                  style: TextStyle(
                    color: Colors.white, // Ensure the text is visible
                    fontSize: 16.0,
                  ),
                ),
                SizedBox(width: 10), // Additional spacing if needed
              ],
            ),
          ),
        ],
        iconTheme: IconThemeData(
          color: Colors.white
        ),
        backgroundColor: Color(0xff4e51ec),
      ),
      drawer: CustomDrawer(),
      body: Column(
        children: [
          Container(
            height: 180, // Adjust the banner height as needed
            decoration: BoxDecoration(
              image: DecorationImage(
                image: widget.internalEvent.picinternalevent == null ? AssetImage("assets/img/internal-thumbnail.jpg") as ImageProvider : NetworkImage("${AppConfig.baseUrl}/img/events/internal/${widget.internalEvent.picinternalevent!}"),
                fit: BoxFit.cover
              ),
            ),
            child: Stack(
              children: [
                Container(
                  color: Colors.black.withOpacity(0.5), // Darkening overlay
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.internalEvent.titleinternalevent, // Replace with your banner text
                          textAlign: TextAlign.start,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20.0,
                              fontWeight: FontWeight.bold
                          ),
                        ),
                        Text(
                          widget.internalEvent.summaryinternalevent, // Replace with your banner text
                          textAlign: TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 15.0
                          ),
                        ),
                        if (widget.internalEvent.frequencyinternalevent != null)
                          Text(
                            generateRepeatSentence(),
                            textAlign: TextAlign.start,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12.0
                            ),
                          ),
                        if (_canDisplay("Paiement en ligne"))
                        Text(
                          widget.internalEvent.nextdateinternalevent != null ? "Prochaine session le " + formatNextDate(widget.internalEvent.nextdateinternalevent!) + "." : "Pas de prochaine session.",
                          textAlign: TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12.0
                          ),
                        ),
                        if (widget.internalEvent.daysbeforelimitinternalevent != null && widget.internalEvent.nextdateinternalevent != null)
                        Text(
                          "Inscription obligatoire avant le " + formatLimitDate(widget.internalEvent.nextdateinternalevent!, widget.internalEvent.daysbeforelimitinternalevent!) + ".",
                          textAlign: TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12.0
                          ),
                        ),
                        if (widget.internalEvent.maxusersinternalevent != null && widget.internalEvent.nextdateinternalevent != null)
                        Text(
                          widget.internalEvent.maxusersinternalevent!.toString() + " places",
                          textAlign: TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12.0
                          ),
                        )
                      ]
                    )
                  ),
                ),
              ],
            ),
          ),
          SafeArea(
            child: SizedBox(
              height: 500,
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
                  child: Html(
                    data: widget.internalEvent.descinternalevent,
                    doNotRenderTheseTags: { 'br' },
                  ),
                ),
              ),
            )
          )
        ],
      ),
    );
  }


}