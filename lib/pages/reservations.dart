import 'dart:core';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:thermassist_mobile/models/reservation.model.dart';
import 'package:thermassist_mobile/pages/internal-event-details.dart';
import 'package:thermassist_mobile/parts/drawer.dart';
import 'package:thermassist_mobile/parts/internal-event-item.dart';
import 'package:thermassist_mobile/parts/reservation-item.dart';
import 'package:thermassist_mobile/services/internal-event.service.dart';
import 'package:thermassist_mobile/services/reservation.service.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/internal-event.model.dart';

class ReservationsPage extends StatefulWidget {
  @override
  _ReservationsPageState createState() => _ReservationsPageState();
}

class _ReservationsPageState extends State<ReservationsPage> {

  List<Reservation>? reservations;

  @override
  initState() {
    super.initState();
    _fetchInternalEvents();
  }

  void _fetchInternalEvents() async {
    final ReservationService reservationService = ReservationService();
    final reservationData = await reservationService.getCurrentReservations();
    setState(() {
      reservations = reservationData;
    });
  }

  void _openBrowser(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  String _convertDateFormat(String date) {
    // Parse the date string into a DateTime object
    DateTime parsedDate = DateTime.parse(date);

    // Format the DateTime object into the desired format
    String formattedDate = DateFormat('dd/MM/yyyy').format(parsedDate);

    return formattedDate;
  }

  void _showContextualMenu(BuildContext context, Reservation reservation) {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      builder: (BuildContext context) {
        return Wrap(
          children: <Widget>[
            if (reservation.paid)
            ListTile(
              leading: const Icon(Icons.receipt),
              title: const Text('Ticket'),
              onTap: () async {
                Navigator.pop(context);
                if (reservation.reservationtype == "ONLINE") {
                  final ReservationService reservationService = ReservationService();
                  String url = await reservationService.getStripeReceiptLink(reservation.reservationid);
                  _openBrowser(url);
                } else {
                  _showReceiptModal(context, reservation);
                }
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.cancel),
              title: const Text('Annuler la réservation'),
              onTap: () {
                Navigator.pop(context);
                // Implement your logic here to handle cancellation
                showCancelConfirmationDialog(context, reservation);
              },
            ),
          ],
        );
      },
    );
  }

  void _showReceiptModal(BuildContext context, Reservation reservation) {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Réservation payée sur place',
                style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8.0),
              Text(
                "Réservation du " + _convertDateFormat(reservation.reservationdate) + " pour la session du " + _convertDateFormat(reservation.eventdate),
                style: TextStyle(fontSize: 16.0),
              ),
              SizedBox(height: 8.0),
              Text(
                reservation.reservationprice.toString() + "€",
                style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> showCancelConfirmationDialog(BuildContext context, Reservation reservation) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // The dialog is not dismissed when tapping outside
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          title: const Text('Confirmer l\'annulation'),
          content: const Text(
            'Êtes-vous sûr de vouloir annuler votre participation à cet évènement ? '
                'Vous recevrez un email de confirmation de la suppression de la réservation.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Annuler'),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
            ),
            TextButton(
              child: const Text('Confirmer'),
              onPressed: () async {
                final ReservationService reservationService = ReservationService();
                await reservationService.cancelReservation(reservation.internaleventid);
                _fetchInternalEvents();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Réservations',
          style: TextStyle(
            color: Colors.white
          )
        ),
        iconTheme: IconThemeData(
          color: Colors.white
        ),
        backgroundColor: Color(0xff4e51ec),
      ),
      drawer: CustomDrawer(),
      body: Column(
        children: [
          Container(
            height: 100, // Adjust the banner height as needed
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/img/reservations.jpg'), // Replace with your image asset
                fit: BoxFit.cover
              ),
            ),
            child: Stack(
              children: [
                Container(
                  color: Colors.black.withOpacity(0.5), // Darkening overlay
                ),
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Réservations', // Replace with your banner text
                          textAlign: TextAlign.start,
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 20.0,
                              fontWeight: FontWeight.bold
                          ),
                        ),
                        Text(
                          'Vos réservations d\'activités internes', // Replace with your banner text
                          textAlign: TextAlign.start,
                          style: TextStyle(
                              color: Colors.grey,
                              fontSize: 15.0
                          ),
                        )
                      ]
                    )
                  ),
                ),
              ],
            ),
          ),
          if (reservations != null)
          Expanded(
              child: ListView.builder(
                itemCount: reservations!.length,
                itemBuilder: (context, index) {
                  final reservation = reservations![index];
                  return GestureDetector(
                    onTap: () {
                      this._showContextualMenu(context, reservation);
                    },
                    child: ReservationItem(
                      title: reservation.internalEvent.titleinternalevent,
                      subtitle: "Réservation du " + _convertDateFormat(reservation.reservationdate) + " pour le " + _convertDateFormat(reservation.eventdate),
                      imageUrl: reservation.internalEvent.picinternalevent,
                      status: reservation.paid ? "Payée" : "En attente de paiement",
                    )
                  );
                },
              )
          )
        ],
      ),
    );
  }


}