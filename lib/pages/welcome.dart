import 'package:flutter/material.dart';
import 'package:thermassist_mobile/icons/thermassist_icons_icons.dart';
import 'package:thermassist_mobile/pages/fork-curist.dart';
import 'package:thermassist_mobile/pages/fork-tourist.dart';
import 'package:thermassist_mobile/pages/login.dart';
import 'package:thermassist_mobile/pages/register-tourist.dart';

import '../parts/background-image.dart';

class WelcomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background Image
          BackgroundImage(imageUrl: 'assets/img/login.png'),
          // Content Container
          Center(
            child: SingleChildScrollView(
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.8), // Add opacity to the background
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 5,
                      blurRadius: 7,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Text(
                      textAlign: TextAlign.center,
                      'Bienvenue sur Thermassist',
                      style: TextStyle(fontSize: 24.0, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 24.0),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildSquareButton(
                          icon: ThermassistIcons.massage,
                          label: 'Je suis curiste',
                          onPressed: () {
                            // Handle the "I am already a user" button press
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => ForkCuristPage()),
                            );
                          },
                        ),
                        _buildSquareButton(
                          icon: Icons.directions_bike,
                          label: 'Je suis touriste',
                          onPressed: () {
                            // Handle the "I'm new here" button press
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => ForkTouristPage()),
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSquareButton({required IconData icon, required String label, required VoidCallback onPressed}) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          Container(
            width: 100.0,
            height: 100.0,
            decoration: const BoxDecoration(
              color: const Color(0xff585bef),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                icon,
                color: Colors.white,
                size: 50.0,
              ),
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            label,
            style: const TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}