import 'package:flutter/material.dart';
import 'package:thermassist_mobile/pages/login.dart';
import 'package:thermassist_mobile/pages/register-tourist.dart';

import '../parts/background-image.dart';

class ForkTouristPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background Image
          BackgroundImage(imageUrl: 'assets/img/login.png'),
          // Content Container
          Center(
            child: SingleChildScrollView(
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.8), // Add opacity to the background
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 5,
                      blurRadius: 7,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Text(
                      'Je suis touriste',
                      style: TextStyle(fontSize: 24.0, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 24.0),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Flexible(
                          child: _buildSquareButton(
                            icon: Icons.person,
                            label: 'J\'ai déjà un compte',
                            onPressed: () {
                              // Handle the "I am already a user" button press
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => LoginPage(), settings: const RouteSettings(name: "/login")),
                              );
                            },
                          ),
                        ),
                        SizedBox(width: 16.0), // Add some spacing between buttons
                        Flexible(
                          child: _buildSquareButton(
                            icon: Icons.person_add,
                            label: 'Je n\'ai pas de compte',
                            onPressed: () {
                              // Handle the "I'm new here" button press
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => RegisterTouristPage()),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 16,
                    ),
                    GestureDetector(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.arrow_back, size: 16),
                          SizedBox(
                            width: 4,
                          ),
                          Text("Retour")
                        ],
                      ),
                      onTap: () => {
                        Navigator.of(context).pop()
                      },
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSquareButton({required IconData icon, required String label, required VoidCallback onPressed}) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          Container(
            width: 100.0,
            height: 100.0,
            decoration: const BoxDecoration(
              color: const Color(0xff585bef),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                icon,
                color: Colors.white,
                size: 50.0,
              ),
            ),
          ),
          const SizedBox(height: 8.0),
          Center(
            child: Text(
              label,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }
}