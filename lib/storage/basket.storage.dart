import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../models/basket-item.model.dart';

class BasketStorage {
  static const _storage = FlutterSecureStorage();
  static const _basketKey = 'basketItems';

  // Save basket items
  static Future<void> saveBasketItems(List<BasketItem> items) async {
    String jsonString = jsonEncode(items.map((item) => item.toJson()).toList());
    await _storage.write(key: _basketKey, value: jsonString);
  }

  // Load basket items
  static Future<List<BasketItem>> loadBasketItems() async {
    String? jsonString = await _storage.read(key: _basketKey);
    if (jsonString == null) {
      return [];
    }
    List<dynamic> jsonList = jsonDecode(jsonString);
    return jsonList.map((json) => BasketItem.fromJson(json)).toList();
  }

  // Clear basket items
  static Future<void> clearBasket() async {
    await _storage.delete(key: _basketKey);
  }
}