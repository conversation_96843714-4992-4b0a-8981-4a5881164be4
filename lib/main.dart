import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:thermassist_mobile/pages/dashboard.dart';
import 'package:thermassist_mobile/pages/login.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:thermassist_mobile/pages/welcome.dart';
import 'package:thermassist_mobile/services/auth.service.dart';
import 'package:thermassist_mobile/services/position.service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:uni_links/uni_links.dart';
import 'firebase_options.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_localizations/flutter_localizations.dart';


final getIt = GetIt.instance;

Future<void> checkTokenAndNavigate() async {
  final storage = new FlutterSecureStorage();
  String? token = await storage.read(key: 'THERMASSIST_TOKEN');

  if (token != null) {
    try {
      final authService = AuthService(); // Replace with your authentication service
      final isValidToken = await authService.whoAmI();
      runApp(const Thermassist(hasValidToken: true));
    } catch (error) {
      runApp(const Thermassist(hasValidToken: false)); // Replace with your main app widget
    }
  } else {
    runApp(const Thermassist(hasValidToken: false));
  }
}

void firebaseInit() async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  initFcm();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
}

void initFcm() async {
  final storage = new FlutterSecureStorage();
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );
  String? token = await messaging.getToken(
    vapidKey: "BJKcOclE1LkfuyWUTTGgGNS7_6pX6d9X984Ew3V3dhyq0d4RPhFzC72jTNVGS6Vf8EEPKhLuSQjt9ElY1wqmzPc",
  );
  await storage.write(key: 'FCM_TOKEN', value: token);
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    print('Got a message whilst in the foreground!');
    print('Message data: ${message.data}');

    if (message.notification != null) {
      print('Message also contained a notification: ${message.notification}');
    }
  });
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print("Handling a background message: ${message.messageId}");
}

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  // Singleton declaration
  getIt.registerSingleton<PositionService>(PositionService());

  // Firebase initialization
  firebaseInit();

  // Uni links initialization
  initUniLinks();

  // Auth check
  checkTokenAndNavigate();
}

Future<void> initUniLinks() async {
  // Platform messages may fail, so we use a try/catch PlatformException.
  try {
    final initialLink = await getInitialLink();
    print(initialLink);
    // Parse the link and warn the user, if it is not correct,
    // but keep in mind it could be `null`.
  } on PlatformException {
    // Handle exception by warning the user their action did not succeed
    // return?
  }
}

class Thermassist extends StatelessWidget {
  final bool hasValidToken;

  const Thermassist({super.key, required this.hasValidToken});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Thermassist',
      debugShowCheckedModeBanner: false,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        DefaultWidgetsLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('fr')
      ],
      locale: const Locale('fr'),
      home:hasValidToken ? DashboardPage() : WelcomePage(),
      routes: {
        '/welcome': (context) => WelcomePage(),
        '/login': (context) => LoginPage(),
        '/next': (context) => DashboardPage(),
      },
      theme: ThemeData(
        // This is the theme of your application.
        //
        // TRY THIS: Try running your application with "flutter run". You'll see
        // the application has a blue toolbar. Then, without quitting the app,
        // try changing the seedColor in the colorScheme below to Colors.green
        // and then invoke "hot reload" (save your changes or press the "hot
        // reload" button in a Flutter-supported IDE, or press "r" if you used
        // the command line to start the app).
        //
        // Notice that the counter didn't reset back to zero; the application
        // state is not lost during the reload. To reset the state, use hot
        // restart instead.
        //
        // This works for code too, not just values: Most code changes can be
        // tested with just a hot reload.
        colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xff585bef)),
        useMaterial3: true,
        textTheme: GoogleFonts.latoTextTheme()
      ),
    );
  }
}