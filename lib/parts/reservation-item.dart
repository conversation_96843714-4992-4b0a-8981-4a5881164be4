import 'package:flutter/material.dart';

import '../config/config.dart';

class ReservationItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String? imageUrl;
  final String status;

  ReservationItem({
    required this.title,
    required this.subtitle,
    this.imageUrl,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      surfaceTintColor: Colors.white,
      child: SizedBox(
        height: 150, // Keep the height consistent to fit the Chip
        child: Row(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8.0),
                bottomLeft: Radius.circular(8.0),
              ),
              child: imageUrl == null
                  ? Image.asset(
                "assets/img/internal-thumbnail.jpg",
                width: 90.0,
                height: 150.0, // Adjust height to match the card
                fit: BoxFit.cover,
              )
                  : Image.network(
                "${AppConfig.baseUrl}/img/events/internal/${imageUrl!}",
                width: 90.0,
                height: 150.0, // Adjust height to match the card
                fit: BoxFit.cover,
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                          fontSize: 14.0, fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4.0), // Small spacing between title and subtitle
                    Text(
                      subtitle,
                      style: const TextStyle(fontSize: 12.0),
                    ),
                    const SizedBox(height: 8.0), // Space between subtitle and chip
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(64.0), // Adjust the border radius as needed
                          color: status == "En attente de paiement" ? Colors.red : Colors.green,
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0), // Adjust padding as needed
                        child: Text(
                          status == "En attente de paiement" ? "En attente de paiement" : "Payée",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 10
                          ),
                        ),
                      )
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}