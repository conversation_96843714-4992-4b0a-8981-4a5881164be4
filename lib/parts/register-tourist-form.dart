import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/pages/dashboard.dart';
import 'package:thermassist_mobile/pages/guest-establishment-select.dart';
import 'package:thermassist_mobile/pages/login.dart';
import 'package:thermassist_mobile/services/establishment.service.dart';

import '../config/config.dart';

class RegisterTouristForm extends StatefulWidget {
  @override
  _RegisterTouristFormState createState() => _RegisterTouristFormState();
}

class _RegisterTouristFormState extends State<RegisterTouristForm> {
  List<Establishment> establishments = []; // Store the select options
  String selectedEstablishmentId = '';

  bool isButtonEnabled = false;
  bool isLoading = false; // Track loading state

  final TextEditingController surnameController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Fetch select options when the page loads
    _fetchPublicEstablishments();

    surnameController.addListener(_validateInputs);
    nameController.addListener(_validateInputs);
    emailController.addListener(_validateInputs);
  }

  void _validateInputs() {
    final surname = surnameController.text.trim();
    final name = nameController.text.trim();
    final email = emailController.text.trim();

    setState(() {
      // Enable the button if both fields are filled, otherwise disable it
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      isButtonEnabled = surname.isNotEmpty && name.isNotEmpty && email.isNotEmpty && emailRegex.hasMatch(emailController.text);
    });
  }

  void _register(BuildContext context) async {
    final dio = Dio();

    setState(() {
      isLoading = true;
    });

    final jsonData = {
      'surname': surnameController.text,
      'name': nameController.text,
      'email': emailController.text,
      'establishmentId': selectedEstablishmentId
    };

    try {
      final response = await dio.post(
        '${AppConfig.baseUrl}/user/register/tourist',
        data: jsonEncode(jsonData),
        options: Options(
          headers: {
            HttpHeaders.contentTypeHeader: "application/json",
          }
        )
      );

      setState(() {
        isLoading = false;
      });

      if (response.statusCode == 200) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Compte créé'),
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              content: Text('La création de votre compte a été effectuée. Vous trouverez vos identifiants dans le mail qui vous a été adressé.'),
              actions: [
                TextButton(
                  onPressed: () =>
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => LoginPage(), settings: const RouteSettings(name: "/login"))),
                  child: Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (error) {
      setState(() {
        isLoading = false;
      });
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Inscription impossible'),
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            content: Text('La création de votre compte n\'a pas abouti. Veuillez contacter le service Thermassist.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  void _skipRegistration(BuildContext context) {
    Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => GuestEstablishmentSelectPage()),
    );
  }

  void _fetchPublicEstablishments() async {
    try {
      final establishmentService = EstablishmentService();
      final establishmentList = await establishmentService.getPublicEstablishments();
      establishmentList.sort((a, b) {
        return a.nameestablishment.compareTo(b.nameestablishment);
      });
      setState(() {
        selectedEstablishmentId = establishmentList[0].idestablishment.toString();
        establishments = establishmentList;
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: SingleChildScrollView(
          child: Stack(
            alignment: Alignment.topLeft,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.8, // Adjust the card width as needed
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 5,
                      blurRadius: 7,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/img/logo_b.png', // Replace with the path to your image
                      height: 100, // Adjust the height as needed
                    ),
                    SizedBox(height: 8.0),
                    const Text(
                      'Bienvenue sur Thermassist !',
                      style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold),
                    ),
                    const Text(
                      'Créez-vous un compte avec les champs suivants.',
                      style: TextStyle(fontSize: 13.0, fontWeight: FontWeight.bold, color: Colors.grey),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the top-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the top-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the username field
                        child: DropdownButtonFormField<String>(
                          value: selectedEstablishmentId,
                          hint: Text('Sélectionnez une option'),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.all(8.0),
                            labelText: 'Station',
                          ),
                          onChanged: (value) {
                            setState(() {
                              selectedEstablishmentId = value!;
                            });
                          },
                          items: establishments.map((option) {
                            return DropdownMenuItem<String>(
                              value: option.idestablishment.toString(),
                              child: Text(option.nameestablishment),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the top-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the top-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the username field
                        child: TextFormField(
                          controller: surnameController,
                          decoration: const InputDecoration(
                            labelText: 'Prénom',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the password field
                        child: TextFormField(
                          controller: nameController,
                          decoration: const InputDecoration(
                            labelText: 'Nom',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the password field
                        child: TextFormField(
                          controller: emailController,
                          decoration: const InputDecoration(
                            labelText: 'Adresse email',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Container(
                          width: double.infinity, // Make the button take the full width
                          child: TextButton(
                            onPressed: () => isButtonEnabled ? _register(context) : null,
                            style: ButtonStyle(
                              backgroundColor: isButtonEnabled ? MaterialStateProperty.all(const Color(0xff585bef)) : MaterialStateProperty.all(const Color(0xffe0e0e0)),
                              foregroundColor: isButtonEnabled ? MaterialStateProperty.all(Colors.white) : MaterialStateProperty.all(const Color(0xffababab)),
                              padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(vertical: 8.0),
                              ),
                              shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.0), // Change the border radius
                                ),
                              ),
                            ),
                            child: Text(
                              (isLoading ? '' : 'Inscription'),
                              style: TextStyle(fontSize: 18.0),
                            ),
                          ),
                        ),
                        if (isLoading)
                          const SizedBox(
                            width: 20.0, // Set the width of the loader
                            height: 20.0, // Set the height of the loader
                            child: CircularProgressIndicator(
                              strokeWidth: 3.0,
                            ),
                          ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () => _skipRegistration(context),
                          child: const Text(
                            'Non merci, je ne veux pas de compte.', // Change the text as needed
                            style: TextStyle(
                              color: Colors.grey, // Change the text color to blue
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Positioned(
                top: 4.0,
                left: 4.0,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Navigate back to the "Welcome" page
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ]
          )
        ),
      ),
    );
  }
}