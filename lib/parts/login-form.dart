import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:thermassist_mobile/pages/dashboard.dart';
import 'package:thermassist_mobile/pages/forgot-password.dart';

import '../config/config.dart';
import '../services/user.service.dart';

class LoginForm extends StatefulWidget {
  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  bool isButtonEnabled = false;
  bool isLoading = false; // Track loading state
  bool passwordVisible = false;

  @override
  void initState() {
    super.initState();
    // Listen to changes in both text fields
    usernameController.addListener(_validateInputs);
    passwordController.addListener(_validateInputs);
  }

  void _validateInputs() {
    final username = usernameController.text.trim();
    final password = passwordController.text.trim();

    setState(() {
      // Enable the button if both fields are filled, otherwise disable it
      isButtonEnabled = username.isNotEmpty && password.isNotEmpty;
    });
  }

  void _login(BuildContext context) async {
    final dio = Dio();

    setState(() {
      isLoading = true;
    });

    final jsonData = {
      'nick': usernameController.text,
      'password': passwordController.text,
      'remember': true
    };

    try {
      final response = await dio.post(
        '${AppConfig.baseUrl}/auth/login',
        data: jsonEncode(jsonData),
        options: Options(
          headers: {
            HttpHeaders.contentTypeHeader: "application/json",
          }
        )
      );

      setState(() {
        isLoading = false;
      });

      if (response.statusCode == 200) {

        final user = response.data['user'];

        if (user['rank']['coderank'] != "TOURIST" && user['rank']['coderank'] != "USER") {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text('Connexion impossible'),
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.white,
                content: Text('L\'application mobile n\'est compatible qu\'avec les comptes curistes et touristes.'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('OK'),
                  ),
                ],
              );
            },
          );
          return;
        }

        // Successful login, store the JWT in secure storage and navigate to homepage
        final jwt = response.data['token'];
        final storage = new FlutterSecureStorage();
        await storage.write(key: 'THERMASSIST_TOKEN', value: jwt);
        await storage.write(key: 'THERMASSIST_ROLE', value: user['rank']['coderank']);
        await storage.write(key: 'THERMASSIST_USERNAME', value: user['nickuser']);
        await storage.write(key: 'THERMASSIST_EMAIL', value: user['emailuser']);
        await storage.write(key: 'THERMASSIST_NAME', value: user['nomuser']);
        await storage.write(key: 'THERMASSIST_SURNAME', value: user['prenomuser']);

        final userService = UserService();
        await userService.updateFcmToken();

        Fluttertoast.showToast(
            msg: "Bon retour sur Thermassist, " + response.data['user']['prenomuser'] + " !",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
            timeInSecForIosWeb: 2,
            backgroundColor: Colors.white,
            textColor: Colors.black,
            fontSize: 16.0
        );

        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => DashboardPage()),
        );
      }
    } catch (error) {
      print(error);
      setState(() {
        isLoading = false;
      });
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Connexion impossible'),
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            content: Text('Votre identifiant ou mot de passe est incorrect.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  void _forgotPassword(BuildContext context) {
    Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => ForgotPasswordPage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: SingleChildScrollView(
          child: Stack(
            alignment: Alignment.topLeft,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.8, // Adjust the card width as needed
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 5,
                      blurRadius: 7,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/img/logo_b.png', // Replace with the path to your image
                      height: 100, // Adjust the height as needed
                    ),
                    SizedBox(height: 8.0),
                    const Text(
                      'Bienvenue sur Thermassist !',
                      style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold),
                    ),
                    const Text(
                      'Connectez-vous avec vos identifiants.',
                      style: TextStyle(fontSize: 13.0, fontWeight: FontWeight.bold, color: Colors.grey),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the top-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the top-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the username field
                        child: TextFormField(
                          controller: usernameController,
                          decoration: const InputDecoration(
                            labelText: 'Identifiant',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the password field
                        child: TextFormField(
                          controller: passwordController,
                          decoration: InputDecoration(
                            labelText: 'Mot de passe',
                            contentPadding: EdgeInsets.all(8.0),
                            suffixIcon: IconButton(
                              icon: Icon(
                                // Based on passwordVisible state choose the icon
                                passwordVisible
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Theme.of(context).primaryColor,
                              ),
                              onPressed: () {
                                // Update the state i.e. toogle the state of passwordVisible variable
                                setState(() {
                                  passwordVisible = !passwordVisible;
                                });
                              },
                            ),
                          ),
                          obscureText: !passwordVisible,
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => _forgotPassword(context),
                          child: const Text(
                            'Mot de passe oublié ?', // Change the text as needed
                            style: TextStyle(
                              color: Colors.grey, // Change the text color to blue
                            ),
                          ),
                        ),
                      ],
                    ),
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Container(
                          width: double.infinity, // Make the button take the full width
                          child: TextButton(
                            onPressed: () => isButtonEnabled ? _login(context) : null,
                            style: ButtonStyle(
                              backgroundColor: isButtonEnabled ? MaterialStateProperty.all(const Color(0xff585bef)) : MaterialStateProperty.all(const Color(0xffe0e0e0)),
                              foregroundColor: isButtonEnabled ? MaterialStateProperty.all(Colors.white) : MaterialStateProperty.all(const Color(0xffababab)),
                              padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(vertical: 8.0),
                              ),
                              shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.0), // Change the border radius
                                ),
                              ),
                            ),
                            child: Text(
                              (isLoading ? '' : 'Connexion'),
                              style: TextStyle(fontSize: 18.0),
                            ),
                          ),
                        ),
                        if (isLoading)
                          const SizedBox(
                            width: 20.0, // Set the width of the loader
                            height: 20.0, // Set the height of the loader
                            child: CircularProgressIndicator(
                              strokeWidth: 3.0,
                            ),
                          ),
                      ],
                    )
                  ],
                ),
              ),
              Positioned(
                top: 4.0,
                left: 4.0,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Navigate back to the "Welcome" page
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ]
          )
        ),
      ),
    );
  }
}