import 'dart:convert';
import 'dart:io';

import 'package:date_field/date_field.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/models/station.model.dart';
import 'package:thermassist_mobile/pages/dashboard.dart';
import 'package:thermassist_mobile/pages/login.dart';
import 'package:thermassist_mobile/services/establishment.service.dart';
import 'package:thermassist_mobile/services/station.service.dart';

import '../config/config.dart';

class RegisterCuristForm extends StatefulWidget {
  @override
  _RegisterCuristFormState createState() => _RegisterCuristFormState();
}

class _RegisterCuristFormState extends State<RegisterCuristForm> {
  List<Establishment> establishments = []; // Store the establishment select options
  String? selectedEstablishmentId;
  List<Station> stations = []; // Store the station select options
  String? selectedStationId;
  DateTime birthdate = DateTime.now();

  bool isButtonEnabled = false;
  bool isLoading = false; // Track loading state

  final TextEditingController surnameController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Fetch select options when the page loads
    _fetchStations();

    surnameController.addListener(_validateInputs);
    nameController.addListener(_validateInputs);
    emailController.addListener(_validateInputs);
  }

  void _validateInputs() {
    final surname = surnameController.text.trim();
    final name = nameController.text.trim();
    final email = emailController.text.trim();

    setState(() {
      // Enable the button if both fields are filled, otherwise disable it
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      isButtonEnabled = surname.isNotEmpty && name.isNotEmpty && email.isNotEmpty && selectedEstablishmentId != null && selectedStationId != null && birthdate != null && emailRegex.hasMatch(emailController.text);
    });
  }

  void _register(BuildContext context) async {
    final dio = Dio();

    setState(() {
      isLoading = true;
    });

    final jsonData = {
      'surname': surnameController.text,
      'name': nameController.text,
      'email': emailController.text,
      'establishmentId': selectedEstablishmentId,
      'birthdate': birthdate?.toIso8601String().split('T')[0]
    };

    try {
      final response = await dio.post(
        '${AppConfig.baseUrl}/user/register/curist',
        data: jsonEncode(jsonData),
        options: Options(
          headers: {
            HttpHeaders.contentTypeHeader: "application/json",
          }
        )
      );

      setState(() {
        isLoading = false;
      });

      if (response.statusCode == 200) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Compte créé'),
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              content: Text('La création de votre compte a été effectuée. Vous trouverez vos identifiants dans le mail qui vous a été adressé.'),
              actions: [
                TextButton(
                  onPressed: () =>
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => LoginPage(), settings: const RouteSettings(name: "/login"))),
                  child: Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (error) {
      setState(() {
        isLoading = false;
      });

      String errorMsg = "La création de votre compte n\'a pas abouti. Veuillez contacter le service Thermassist.";
      if ((error as DioException).response?.statusCode == 409) {
        errorMsg = "La création de votre compte n'a pas abouti. Votre établissement de cure vous a peut être déjà créé un compte. Si vous ne retrouvez pas un email ayant pour objet 'Vos identifiants Thermassist', veuillez nous contacter à l'adresse <EMAIL>.";
      }

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Inscription impossible'),
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            content: Text(errorMsg),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: birthdate,
        firstDate: DateTime(1900, 1),
        lastDate: DateTime.now());
    if (picked != null && picked != birthdate) {
      setState(() {
        birthdate = picked;
      });
    }
  }

  void _skipRegistration(BuildContext context) {
    print("Skip registration...");
  }

  void _fetchStations() async {
    try {
      final stationService = StationService();
      final stationList = await stationService.getStations();
      stationList.sort((a, b) {
        return a.namestation.compareTo(b.namestation);
      });
      setState(() {
        stations = stationList;
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _fetchEstablishmentsForStation(int stationId) async {
    try {
      final establishmentService = EstablishmentService();
      final establishmentList = await establishmentService.getEstablishmentsForStation(stationId);
      establishmentList.sort((a, b) {
        return a.nameestablishment.compareTo(b.nameestablishment);
      });
      setState(() {
        establishments = establishmentList;
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: SingleChildScrollView(
          child: Stack(
            alignment: Alignment.topLeft,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.8, // Adjust the card width as needed
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 5,
                      blurRadius: 7,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/img/logo_b.png', // Replace with the path to your image
                      height: 100, // Adjust the height as needed
                    ),
                    SizedBox(height: 8.0),
                    const Text(
                      'Bienvenue sur Thermassist !',
                      style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold),
                    ),
                    const Text(
                      'Créez-vous un compte avec les champs suivants.',
                      style: TextStyle(fontSize: 13.0, fontWeight: FontWeight.bold, color: Colors.grey),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the top-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the top-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the username field
                        child: DropdownButtonFormField<String>(
                          value: selectedStationId,
                          hint: Text('Sélectionnez une option'),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.all(8.0),
                            labelText: 'Station',
                          ),
                          onChanged: (value) {
                            setState(() {
                              selectedStationId = value!;
                              _fetchEstablishmentsForStation(int.parse(value));
                            });
                          },
                          items: stations.map((option) {
                            return DropdownMenuItem<String>(
                              value: option.idstation.toString(),
                              child: Text(option.namestation),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                    if (selectedStationId != null)
                    const SizedBox(height: 16.0),
                    if (selectedStationId != null)
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the top-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the top-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the username field
                        child: DropdownButtonFormField<String>(
                          value: selectedEstablishmentId,
                          hint: Text('Sélectionnez une option'),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.all(8.0),
                            labelText: 'Etablissement',
                          ),
                          onChanged: (value) {
                            setState(() {
                              selectedEstablishmentId = value;
                            });
                          },
                          items: establishments.map((option) {
                            return DropdownMenuItem<String>(
                              value: option.idestablishment.toString(),
                              child: Text(option.nameestablishment),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the top-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the top-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the username field
                        child: TextFormField(
                          controller: surnameController,
                          decoration: const InputDecoration(
                            labelText: 'Prénom',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the password field
                        child: TextFormField(
                          controller: nameController,
                          decoration: const InputDecoration(
                            labelText: 'Nom',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the password field
                        child: TextFormField(
                          controller: emailController,
                          decoration: const InputDecoration(
                            labelText: 'Adresse email',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0),
                        topRight: Radius.circular(4.0),
                      ),
                      child: Container(
                        color: Colors.grey[200],
                        child: DateTimeFormField(
                          mode: DateTimeFieldPickerMode.date,
                          onChanged: (DateTime? value) {
                            setState(() {
                              if (value != null) {
                                birthdate = value;
                              }
                            });
                          },
                          decoration: const InputDecoration(
                            labelText: 'Date de naissance',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Container(
                          width: double.infinity, // Make the button take the full width
                          child: TextButton(
                            onPressed: () => isButtonEnabled ? _register(context) : null,
                            style: ButtonStyle(
                              backgroundColor: isButtonEnabled ? MaterialStateProperty.all(const Color(0xff585bef)) : MaterialStateProperty.all(const Color(0xffe0e0e0)),
                              foregroundColor: isButtonEnabled ? MaterialStateProperty.all(Colors.white) : MaterialStateProperty.all(const Color(0xffababab)),
                              padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(vertical: 8.0),
                              ),
                              shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.0), // Change the border radius
                                ),
                              ),
                            ),
                            child: Text(
                              (isLoading ? '' : 'Inscription'),
                              style: TextStyle(fontSize: 18.0),
                            ),
                          ),
                        ),
                        if (isLoading)
                          const SizedBox(
                            width: 20.0, // Set the width of the loader
                            height: 20.0, // Set the height of the loader
                            child: CircularProgressIndicator(
                              strokeWidth: 3.0,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              Positioned(
                top: 4.0,
                left: 4.0,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Navigate back to the "Welcome" page
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ]
          )
        ),
      ),
    );
  }
}