import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:thermassist_mobile/pages/dashboard.dart';

import '../config/config.dart';

class ForgotPasswordForm extends StatefulWidget {
  @override
  _ForgotPasswordFormState createState() => _ForgotPasswordFormState();
}

class _ForgotPasswordFormState extends State<ForgotPasswordForm> {
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  bool isButtonEnabled = false;
  bool isLoading = false; // Track loading state

  @override
  void initState() {
    super.initState();
    // Listen to changes in both text fields
    usernameController.addListener(_validateInputs);
    emailController.addListener(_validateInputs);
  }

  void _validateInputs() {
    final username = usernameController.text.trim();
    final email = emailController.text.trim();

    setState(() {
      // Enable the button if both fields are filled, otherwise disable it
      isButtonEnabled = username.isNotEmpty && email.isNotEmpty;
    });
  }

  void _reset(BuildContext context) async {
    final dio = Dio();

    setState(() {
      isLoading = true;
    });

    final jsonData = {
      'nick': usernameController.text,
      'email': emailController.text
    };

    try {
      final response = await dio.post(
        '${AppConfig.baseUrl}/auth/forgotpw',
        data: jsonEncode(jsonData),
        options: Options(
          headers: {
            HttpHeaders.contentTypeHeader: "application/json",
          }
        )
      );

      setState(() {
        isLoading = false;
      });

      if (response.statusCode == 200) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Compte réinitialisé'),
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              content: Text('Votre mot de passe a été réinitialisé. Vous le trouverez dans l\'email qui vous a été adressé.'),
              actions: [
                TextButton(
                  onPressed: () =>
                      Navigator.popUntil(context, (route) => route.settings.name == "/login"),
                  child: Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (error) {
      setState(() {
        isLoading = false;
      });
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Réinitialisation impossible'),
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            content: Text('Avez-vous bien renseigné vos identifiant ou votre adresse email ?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  void _login(BuildContext context) {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: SingleChildScrollView(
          child: Stack(
            alignment: Alignment.topLeft,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.8, // Adjust the card width as needed
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 5,
                      blurRadius: 7,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/img/logo_b.png', // Replace with the path to your image
                      height: 100, // Adjust the height as needed
                    ),
                    SizedBox(height: 8.0),
                    const Text(
                      'Mot de passe oublié ?',
                      style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold),
                    ),
                    const Text(
                      'Entrez vos informations pour en recevoir un nouveau.',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 13.0, fontWeight: FontWeight.bold, color: Colors.grey),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the top-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the top-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the username field
                        child: TextFormField(
                          controller: usernameController,
                          decoration: const InputDecoration(
                            labelText: 'Identifiant',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the bottom-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the bottom-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the password field
                        child: TextFormField(
                          controller: emailController,
                          decoration: const InputDecoration(
                            labelText: 'Adresse email',
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                          obscureText: false,
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => _login(context),
                          child: const Text(
                            'Vous souaitez plutôt vous connecter ?', // Change the text as needed
                            style: TextStyle(
                              color: Colors.grey, // Change the text color to blue
                            ),
                          ),
                        ),
                      ],
                    ),
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Container(
                          width: double.infinity, // Make the button take the full width
                          child: TextButton(
                            onPressed: () => isButtonEnabled ? _reset(context) : null,
                            style: ButtonStyle(
                              backgroundColor: isButtonEnabled ? MaterialStateProperty.all(const Color(0xff585bef)) : MaterialStateProperty.all(const Color(0xffe0e0e0)),
                              foregroundColor: isButtonEnabled ? MaterialStateProperty.all(Colors.white) : MaterialStateProperty.all(const Color(0xffababab)),
                              padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(vertical: 8.0),
                              ),
                              shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.0), // Change the border radius
                                ),
                              ),
                            ),
                            child: Text(
                              (isLoading ? '' : 'Réinitialiser'),
                              style: TextStyle(fontSize: 18.0),
                            ),
                          ),
                        ),
                        if (isLoading)
                          const SizedBox(
                            width: 20.0, // Set the width of the loader
                            height: 20.0, // Set the height of the loader
                            child: CircularProgressIndicator(
                              strokeWidth: 3.0,
                            ),
                          ),
                      ],
                    )
                  ],
                ),
              ),
              Positioned(
                top: 4.0,
                left: 4.0,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Navigate back to the "Welcome" page
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ]
          )
        ),
      ),
    );
  }
}