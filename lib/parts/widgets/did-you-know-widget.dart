import 'dart:math';

import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';

import '../../models/knowledge.model.dart';
import '../../services/knowledge.service.dart';

class DidYouKnowWidget extends StatefulWidget {
  @override
  _DidYouKnowWidgetState createState() => _DidYouKnowWidgetState();
}

class _DidYouKnowWidgetState extends State<DidYouKnowWidget> {

  Knowledge? knowledge;


  @override
  void initState() {
    super.initState();
    _fetchKnowledge();
  }

  void _fetchKnowledge() async {
    final knowledgeService = KnowledgeService();
    final knowledgeList = await knowledgeService.getCurrentKnowledge();
    setState(() {
      knowledge = knowledgeList[Random().nextInt(knowledgeList.length)];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Card(
          surfaceTintColor: Colors.white,
          color: Colors.white,
          margin: EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
          child: Column(
            children: [
              ListTile(
                leading: Icon(Icons.question_mark,
                  size: 40,
                ),
                title: Text(
                  "Le saviez-vous ?",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                subtitle:
                Text(
                  (knowledge != null) ? knowledge!.contentknowledge : "",
                  style: TextStyle(
                    fontSize: 16.0,
                  ),
                ),
              ),
            ],
          ),
        )
      ),
    );
  }
}