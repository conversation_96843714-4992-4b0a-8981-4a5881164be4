import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';

class AnnouncementWidget extends StatefulWidget {
  final String content;

  AnnouncementWidget({ required this.content });

  @override
  _AnnouncementWidgetState createState() => _AnnouncementWidgetState();
}

class _AnnouncementWidgetState extends State<AnnouncementWidget> {


  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Card(
        surfaceTintColor: Colors.white,
        color: Colors.white,
        margin: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
        child: Sized<PERSON><PERSON>(
          height: 40,
          child: <PERSON><PERSON><PERSON>(
            text: widget.content,
            style: TextStyle(fontWeight: FontWeight.bold),
            scrollAxis: Axis.horizontal,
            crossAxisAlignment: CrossAxisAlignment.center,
            blankSpace: 20.0,
            velocity: 100.0,
            startPadding: 10.0,
          )
      ),
      )
    );
  }
}