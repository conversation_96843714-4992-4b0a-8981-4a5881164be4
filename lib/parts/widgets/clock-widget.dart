import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';

class ClockWidget extends StatefulWidget {
  @override
  _ClockWidgetState createState() => _ClockWidgetState();
}

class _ClockWidgetState extends State<ClockWidget> {

  late final String currentTime;
  late final String currentDay;

  @override
  void initState() {
    super.initState();
    initializeDateFormatting("fr");
    currentTime = DateFormat.Hm().format(DateTime.now());
    currentDay = DateFormat('E d MMMM', 'fr').format(DateTime.now());
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Card(
          surfaceTintColor: Colors.white,
          color: Colors.white,
          margin: EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
          child: Column(
            children: [
              ListTile(
                leading: Icon(Icons.watch,
                  size: 40,
                ),
                title: Text(
                  currentTime,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                subtitle:
                Text(
                  currentDay,
                  style: TextStyle(
                    fontSize: 14.0,
                  ),
                ),
              ),
            ],
          ),
        )
      ),
    );
  }
}