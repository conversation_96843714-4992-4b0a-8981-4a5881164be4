import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:thermassist_mobile/pages/internal-events.dart';
import 'package:thermassist_mobile/parts/widgets/events-widget-item.dart';
import 'package:thermassist_mobile/services/internal-event.service.dart';

import '../../models/internal-event.model.dart';

class EventsWidget extends StatefulWidget {
  @override
  _EventsWidgetState createState() => _EventsWidgetState();
}

class _EventsWidgetState extends State<EventsWidget> {

  List<InternalEvent>? internalEvents;

  @override
  void initState() {
    super.initState();
    _fetchEvents();
  }

  void _fetchEvents() async {
    final InternalEventService internalEventService = InternalEventService();
    final internalEventsData = await internalEventService.getCurrentInternalEvents();
    internalEventsData.sort((a, b) => b.idinternalevent.compareTo(a.idinternalevent));
    setState(() {
      internalEvents = internalEventsData;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Card(
          surfaceTintColor: Colors.white,
          color: Colors.white,
          margin: EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Text("Activités internes récentes",
                      style: TextStyle(
                        fontWeight: FontWeight.bold
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 8.0,
                ),
                if (internalEvents != null)
                SizedBox(
                    height: 200,
                    width: double.infinity,
                    child: ListView.builder(
                      itemCount: internalEvents!.length,
                      itemBuilder: (context, index) {
                        final item = internalEvents![index];
                        return GestureDetector(
                          child: EventsWidgetItem(
                            title: item.titleinternalevent,
                            subtitle: item.summaryinternalevent,
                            imageUrl: item.picinternalevent,
                          ),
                          onTap: () => {
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => InternalEventsPage()),
                            )
                          },
                        );
                      },
                    )
                )
              ],
            )
        ),
      )
    ));
  }
}