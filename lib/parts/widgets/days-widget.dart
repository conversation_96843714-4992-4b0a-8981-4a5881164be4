import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:thermassist_mobile/models/remaining-days.model.dart';
import 'package:thermassist_mobile/services/user.service.dart';

class DaysWidget extends StatefulWidget {
  @override
  _DaysWidgetState createState() => _DaysWidgetState();
}

class _DaysWidgetState extends State<DaysWidget> {

  RemainingDays? remainingDays;

  @override
  void initState() {
    super.initState();
    _fetchRemainingDays();
  }

  void _fetchRemainingDays() async {
    final userService = UserService();
    final days = await userService.getRemainingDays();
    setState(() {
      remainingDays = days;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Card(
          surfaceTintColor: Colors.white,
          color: Colors.white,
          margin: EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
          child: Column(
            children: [
              if (remainingDays != null)
              ListTile(
                leading: Icon(Icons.access_time_filled,
                  size: 40,
                ),
                title: Text(
                  remainingDays!.days < 0 ? "Une cure dure 21 jours." : "Il vous reste ${remainingDays!.days} jours !",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                subtitle:
                Text(
                  remainingDays!.days < 0  ? "Trois semaines pour prendre soin de vous !" : "Nous vous souhaitons un excellent séjour.",
                  style: TextStyle(
                    fontSize: 14.0,
                  ),
                ),
              ),
            ],
          ),
        )
      ),
    );
  }
}