import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';

class VideoWidget extends StatefulWidget {
  final String url;

  VideoWidget({ required this.url });

  @override
  _VideoWidgetState createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> {

  final _controller = YoutubePlayerController(
    params: const YoutubePlayerParams(
      mute: false,
      showControls: true,
      showFullscreenButton: true
    ),
  );

  @override
  void initState() {
    super.initState();
    _controller.cueVideoById(videoId: widget.url.split("?")[0]);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Card(
        surfaceTintColor: Colors.white,
        color: Colors.white,
        margin: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: SizedBox(
              height: 250.0,
              width: double.infinity,
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0)
                ),
                child: YoutubePlayer(
                  controller: _controller,
                  aspectRatio: 16 / 9,
                ),
              )
          ),
        )
      )
    );
  }
}