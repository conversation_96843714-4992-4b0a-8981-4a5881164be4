import 'package:flutter/material.dart';

import '../../config/config.dart';

class EventsWidgetItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String? imageUrl;

  EventsWidgetItem({required this.title, required this.subtitle, this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      surfaceTintColor: Colors.white,
      child: SizedBox(
        height: 60,
        child: Row(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8.0), // Adjust the radius as needed
                bottomLeft: Radius.circular(8.0), // Adjust the radius as needed
              ),
              child:
              imageUrl == null ?
                  Image.asset(
                      "assets/img/internal-thumbnail.jpg",
                      width: 60.0, // Adjust image width as needed
                      height: 60.0, // Adjust image height as needed
                      fit: BoxFit.cover,
                  )
              :
                Image.network(
                  "${AppConfig.baseUrl}/img/events/internal/${imageUrl!}", // Replace with your image URL
                  width: 60.0, // Adjust image width as needed
                  height: 60.0, // Adjust image height as needed
                  fit: BoxFit.cover, // Adjust image fit as needed
                ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(8.0), // Adjust padding as needed
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12.0,
                        overflow: TextOverflow.ellipsis, // Truncate overflow
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      )
    );
  }
}