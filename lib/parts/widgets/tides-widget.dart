import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:thermassist_mobile/models/marea.model.dart';
import 'package:thermassist_mobile/services/marea.service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:thermassist_mobile/services/position.service.dart';

class TidesWidget extends StatefulWidget {
  @override
  _TidesWidgetState createState() => _TidesWidgetState();
}

class _TidesWidgetState extends State<TidesWidget> {

  Marea? tides;

  @override
  void initState() {
    super.initState();
    _getTideData();
  }

  void _getTideData() async {
    Position position = await GetIt.instance<PositionService>().determinePosition();
    _fetchMareaData(position);
  }

  void _fetchMareaData(Position position) async {
    final mareaService = MareaService();
    final tidesData = await mareaService.getTides(position.latitude, position.longitude);
    setState(() {
      tides = tidesData;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Card(
          surfaceTintColor: Colors.white,
          color: Colors.white,
          margin: EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
          child: SizedBox(
            width: double.infinity,
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                children: [
                  const Row(
                    children: [
                      Text("Marées",
                        style: TextStyle(
                            fontWeight: FontWeight.bold
                        ),
                      ),
                    ],
                  ),
                  if (tides != null)
                  Row(
                    children: [
                      const Text("La marée est actuellement ",
                        style: TextStyle(
                          fontSize: 12.0
                        )
                      ),
                      Text("${tides!.heights.first.state == "RISING" ? "montante" : "descendante"}.",
                        style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12.0
                        ),
                      ),
                    ],
                  ),
                  if (tides != null)
                    SizedBox(
                      width: double.infinity,
                      height: 90,
                      child: GridView.builder(
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 4
                        ),
                        itemCount: tides!.extremes.length,
                        itemBuilder: (BuildContext context, int index) {
                          final tide = tides!.extremes[index];
                          final time = DateFormat('HH:mm').format(DateTime.parse(tide.datetime));
                          return Row(
                            children: [
                              // Left: Icon (Replace with your icon)
                              Icon(tides!.extremes[index].state == "LOW TIDE" ? Icons.vertical_align_bottom : Icons.vertical_align_top, size: 20.0),
                              const SizedBox(
                                width: 16.0,
                              ),
                              // Right: Column with Time and Height
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text("${time} - ${tide.state == "LOW TIDE" ? "Marée basse" : "Marée haute"}",
                                      style: const TextStyle(
                                          fontSize: 12.0
                                      ),
                                    ),
                                    Text('${tide.height.toStringAsFixed(2)}m',
                                      style: const TextStyle(
                                          fontSize: 10.0,
                                          color: Colors.grey
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    )
                ],
              )
            )
          )
        )
      ),
    );
  }
}