import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:thermassist_mobile/models/weather.model.dart';
import 'package:thermassist_mobile/services/position.service.dart';

import '../../models/weather-forecast.model.dart';
import '../../services/weather.service.dart';

class WeatherWidget extends StatefulWidget {
  @override
  _WeatherWidgetState createState() => _WeatherWidgetState();
}

class _WeatherWidgetState extends State<WeatherWidget> {

  Weather? weather;
  WeatherForecast? weatherForecast;

  @override
  void initState() {
    super.initState();
    _getWeatherData();
  }

  void _getWeatherData() async {
    Position position = await  GetIt.instance<PositionService>().determinePosition();
    _fetchWeatherData(position);
    _fetchForecastData(position);
  }

  void _fetchWeatherData(Position position) async {
    final weatherService = WeatherService();
    final weatherData = await weatherService.getWeather(position.latitude, position.longitude);
    setState(() {
      weather = weatherData;
    });
  }

  void _fetchForecastData(Position position) async {
    final weatherService = WeatherService();
    final forecastData = await weatherService.getForecast(position.latitude, position.longitude);
    setState(() {
      weatherForecast = forecastData;
    });
  }



  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Card(
          surfaceTintColor: Colors.white,
          color: Colors.white,
          margin: EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              children: [
                if (weather != null)
                Row(
                  children: [
                    Text("Météo",
                      style: TextStyle(
                        fontWeight: FontWeight.bold
                      ),
                    ),
                    SizedBox(
                      width: 16.0,
                    ),
                    Icon(Icons.location_on,
                      size: 16.0,
                      color: Colors.grey,
                    ),
                    SizedBox(
                      width: 4.0,
                    ),
                    Text(weather!.location.name,
                      style: TextStyle(
                        color: Colors.grey
                      ),
                    )
                  ],
                ),
                if (weather != null)
                // First Row: Current Weather
                Row(
                  children: [
                    Image.network(
                      weather!.weatherState.weatherIconUrl
                    ),
                    SizedBox(
                        width: 16.0
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("Actuellement ${weather?.weatherState.description}, ${weather?.temperature.value.round()}°C", style: TextStyle(fontSize: 14.0)),
                        Text("Ressenti ${weather?.temperature.feelsLike.round()}°C", style: TextStyle(fontSize: 10.0, color: Colors.grey)),
                      ],
                    ),
                  ],
                ),
                SizedBox(
                    height: 8.0
                ),
                // Second Row: Forecast
                if (weatherForecast != null)
                Container(
                  height: 70.0, // Adjust the height of the forecast area
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: weatherForecast!.weatherForecasts.length,
                    itemBuilder: (BuildContext context, int index) {
                      final forecast = weatherForecast!.weatherForecasts[index];
                      final time = DateFormat('HH:mm').format(DateTime.parse(forecast.forecastTime));
                      return Padding(
                          padding: EdgeInsets.fromLTRB(4.0, 0.0, 4.0, 0.0),
                          child: Column(
                            children: [
                              Image.network(
                                width: 32.0,
                                height: 32.0,
                                forecast.weatherState.weatherIconUrl
                              ),
                              Text(time,
                                style: TextStyle(
                                  fontSize: 10.0
                                ),
                              ),
                              Text("${forecast.temperature.value.round()}°C",
                                style: TextStyle(
                                    fontSize: 10.0,
                                    color: Colors.grey
                                ),
                              ),
                            ],
                          )
                      );
                    },
                  ),
                ),
              ],
            ),
          )
        )
      ),
    );
  }
}