import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';
import 'package:url_launcher/url_launcher.dart';

class ImageWidget extends StatefulWidget {
  final String url;
  final String? title;
  final String? link;

  ImageWidget({ required this.url, required this.title, required this.link });

  @override
  _ImageWidgetState createState() => _ImageWidgetState();
}

class _ImageWidgetState extends State<ImageWidget> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Card(
        surfaceTintColor: Colors.white,
        color: Colors.white,
        margin: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
        child: GestureDetector(
          onTap: () async {
            if (widget.link == null) return;
            if (await canLaunch(widget.link!)) {
              await launch(widget.link!);
            } else {
              throw 'Could not launch ${widget.link}';
            }
          },
          child: Container(
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Stack(
                    children: [
                      Image.network(
                        widget.url,
                        fit: BoxFit.cover,
                        width: double.infinity,
                      ),
                      Positioned(
                        left: 8.0,
                        bottom: 8.0,
                        width: MediaQuery.of(context).size.width - 32.0,
                        child: Text(
                          widget.title ?? "",
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20.0,
                            fontWeight: FontWeight.bold,
                          ),
                          softWrap: true,
                        ),
                      ),
                    ],
                  )
              )
          ),
        )
      )
    );
  }
}