import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/pages/dashboard.dart';
import 'package:thermassist_mobile/pages/login.dart';
import 'package:thermassist_mobile/services/establishment.service.dart';

import '../config/config.dart';

class GuestEstablishmentSelectForm extends StatefulWidget {
  @override
  _GuestEstablishmentSelectFormState createState() => _GuestEstablishmentSelectFormState();
}

class _GuestEstablishmentSelectFormState extends State<GuestEstablishmentSelectForm> {
  List<Establishment> establishments = []; // Store the select options
  String selectedEstablishmentId = '';

  bool isLoading = false; // Track loading state

  @override
  void initState() {
    super.initState();
    // Fetch select options when the page loads
    _fetchPublicEstablishments();
  }

  Future<void> _submit(BuildContext context) async {
    final dio = Dio();

    setState(() {
      isLoading = true;
    });

    final jsonData = {
      'establishmentId': selectedEstablishmentId
    };

    try {
      final response = await dio.post(
          '${AppConfig.baseUrl}/auth/anonymous',
          data: jsonEncode(jsonData),
          options: Options(
              headers: {
                HttpHeaders.contentTypeHeader: "application/json",
              }
          )
      );

      setState(() {
        isLoading = false;
      });

      if (response.statusCode == 200) {

        final user = response.data['user'];

        // Successful login, store the JWT in secure storage and navigate to homepage
        final jwt = response.data['token'];
        final storage = new FlutterSecureStorage();
        await storage.write(key: 'THERMASSIST_TOKEN', value: jwt);
        await storage.write(key: 'THERMASSIST_ROLE', value: "TOURIST");
        await storage.write(key: 'THERMASSIST_USERNAME', value: "Anonyme");
        await storage.write(key: 'THERMASSIST_EMAIL', value: "Anonyme");
        await storage.write(key: 'THERMASSIST_NAME', value: "Anonyme");
        await storage.write(key: 'THERMASSIST_SURNAME', value: "Anonyme");

        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => DashboardPage()),
        );
      }
    } catch (error) {
      print(error);
      setState(() {
        isLoading = false;
      });
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Connexion impossible'),
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            content: Text('Une erreur inconnue s\'est produite.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  void _createAccount(BuildContext context) {
    Navigator.pop(context);
  }

  void _fetchPublicEstablishments() async {
    try {
      final establishmentService = EstablishmentService();
      final establishmentList = await establishmentService.getPublicEstablishments();
      establishmentList.sort((a, b) {
        return a.nameestablishment.compareTo(b.nameestablishment);
      });
      setState(() {
        selectedEstablishmentId = establishmentList[0].idestablishment.toString();
        establishments = establishmentList;
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: SingleChildScrollView(
          child: Stack(
            alignment: Alignment.topLeft,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.8, // Adjust the card width as needed
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 5,
                      blurRadius: 7,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/img/logo_b.png', // Replace with the path to your image
                      height: 100, // Adjust the height as needed
                    ),
                    SizedBox(height: 8.0),
                    const Text(
                      'Bienvenue sur Thermassist !',
                      style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold),
                    ),
                    const Text(
                      'Choisissez dans quel espace vous souhaitez être invité.',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 13.0, fontWeight: FontWeight.bold, color: Colors.grey),
                    ),
                    const SizedBox(height: 16.0),
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4.0), // Adjust the top-left corner radius
                        topRight: Radius.circular(4.0), // Adjust the top-right corner radius
                      ),
                      child: Container(
                        color: Colors.grey[200], // Set the background color for the username field
                        child: DropdownButtonFormField<String>(
                          value: selectedEstablishmentId,
                          hint: Text('Sélectionnez une option'),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.all(8.0),
                            labelText: 'Station',
                          ),
                          onChanged: (value) {
                            setState(() {
                              selectedEstablishmentId = value!;
                            });
                          },
                          items: establishments.map((option) {
                            return DropdownMenuItem<String>(
                              value: option.idestablishment.toString(),
                              child: Text(option.nameestablishment),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Container(
                          width: double.infinity, // Make the button take the full width
                          child: TextButton(
                            onPressed: () => _submit(context),
                            style: ButtonStyle(
                              backgroundColor: MaterialStateProperty.all(const Color(0xff585bef)),
                              foregroundColor: MaterialStateProperty.all(Colors.white),
                              padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(vertical: 8.0),
                              ),
                              shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.0), // Change the border radius
                                ),
                              ),
                            ),
                            child: Text(
                              (isLoading ? '' : 'Démarrer'),
                              style: TextStyle(fontSize: 18.0),
                            ),
                          ),
                        ),
                        if (isLoading)
                          const SizedBox(
                            width: 20.0, // Set the width of the loader
                            height: 20.0, // Set the height of the loader
                            child: CircularProgressIndicator(
                              strokeWidth: 3.0,
                            ),
                          ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () => _createAccount(context),
                          child: const Text(
                            'Finalement, j\'aimerais un compte.', // Change the text as needed
                            style: TextStyle(
                              color: Colors.grey, // Change the text color to blue
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Positioned(
                top: 4.0,
                left: 4.0,
                child: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Navigate back to the "Welcome" page
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ]
          )
        ),
      ),
    );
  }
}