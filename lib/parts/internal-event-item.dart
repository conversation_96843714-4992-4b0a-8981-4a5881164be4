import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../config/config.dart';
import '../models/internal-event-status.model.dart';

class InternalEventItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String? imageUrl;
  final String? nextDate;
  final int? maxUsers;
  final InternalEventStatus? status;

  InternalEventItem({required this.title, required this.subtitle, this.imageUrl, this.nextDate, this.maxUsers, this.status});

  String formatNextDate(String nextDate) {
    // Parse the string into a DateTime object
    DateTime parsedDate = DateTime.parse(nextDate);

    // Format the DateTime object into the desired format
    String formattedDate = "${DateFormat('dd/MM/yyyy à HH:mm').format(parsedDate)}h";

    return formattedDate;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      surfaceTintColor: Colors.white,
      child: SizedBox(
        height: 120,
        child: Row(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8.0), // Adjust the radius as needed
                bottomLeft: Radius.circular(8.0), // Adjust the radius as needed
              ),
              child: imageUrl == null ?
              Image.asset(
                "assets/img/internal-thumbnail.jpg",
                width: 90.0, // Adjust image width as needed
                height: 120.0, // Adjust image height as needed
                fit: BoxFit.cover,
              )
                  :
              Image.network(
                "${AppConfig.baseUrl}/img/events/internal/${imageUrl!}", // Replace with your image URL
                width: 90.0, // Adjust image width as needed
                height: 120.0, // Adjust image height as needed
                fit: BoxFit.cover, // Adjust image fit as needed
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(8.0), // Adjust padding as needed
                child: ClipRect(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        nextDate != null ? formatNextDate(nextDate!) + " - " + maxUsers.toString() + " places" : "Aucune session prévue.",
                        style: TextStyle(fontSize: 12.0, color: Colors.black54)
                      ),
                      Text(
                        subtitle,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 12.0),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      )
    );
  }
}