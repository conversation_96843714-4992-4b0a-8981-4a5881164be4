import 'package:flutter/material.dart';

class SirtaquiEventItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String? imageUrl;

  SirtaquiEventItem({required this.title, required this.subtitle, this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      surfaceTintColor: Colors.white,
      child: SizedBox(
        height: 90,
        child: Row(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8.0), // Adjust the radius as needed
                bottomLeft: Radius.circular(8.0), // Adjust the radius as needed
              ),
              child: Image.network(
                imageUrl == null ? "https://images.prismic.io/peopleforbikes/e4073026-1530-40d8-a2d8-6d2b88ff2874_BB_0504.png?auto=compress,format" : imageUrl!,
                width: 90.0, // Adjust image width as needed
                height: 90.0, // Adjust image height as needed
                fit: BoxFit.cover, // Adjust image fit as needed
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(8.0), // Adjust padding as needed
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 12.0
                      ),
                      maxLines: 2, // Maximum lines of text to display
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      )
    );
  }
}