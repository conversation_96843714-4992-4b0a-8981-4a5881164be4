import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/establishment-contact.model.dart';
import 'package:thermassist_mobile/models/establishment-number.model.dart';
import 'package:thermassist_mobile/models/service-config.model.dart';
import 'package:thermassist_mobile/pages/dashboard.dart';
import 'package:thermassist_mobile/pages/external-events.dart';
import 'package:thermassist_mobile/pages/internal-events.dart';
import 'package:thermassist_mobile/pages/profile.dart';
import 'package:thermassist_mobile/pages/reservations.dart';
import 'package:thermassist_mobile/pages/welcome.dart';
import 'package:thermassist_mobile/services/establishment.service.dart';
import 'package:thermassist_mobile/services/settings.service.dart';
import 'package:url_launcher/url_launcher.dart';

import '../models/user.model.dart';

class CustomDrawer extends StatefulWidget {
  @override
  _CustomDrawerState createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {

  User? user;
  String? userRank;
  String? username;
  String? userMail;
  String? userName;
  String? userSurname;
  EstablishmentContact? establishmentContact;
  List<EstablishmentNumber>? establishmentNumbers;
  List<ServiceConfig>? serviceSettings;

  bool isLoading = true; // Loading state

  @override
  void initState() {
    super.initState();
    _fetchUserData();
    _fetchEstablishmentData();
    _fetchSettings();
  }

  void _showContactBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      builder: (BuildContext context) {
        return _buildBottomSheetContent();
      },
    );
  }

  _launchCaller(String phoneNumber) async {
    String url = "tel:$phoneNumber";
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  _launchMail(String email) async {
    String url = "mailto:$email";
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  Widget _buildBottomSheetContent() {
    return Wrap(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Informations de contact",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              Visibility(
                visible: establishmentContact != null && ((establishmentContact?.emailEstablishment != null && establishmentContact?.emailEstablishment != "") || (establishmentContact?.numberEstablishment != null && establishmentContact?.numberEstablishment != "") || (establishmentContact?.addressEstablishment != null && establishmentContact?.addressEstablishment != "")),
                child: Container(
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Color(0xff585bef), width: 1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Visibility(
                      visible: establishmentContact != null && ((establishmentContact?.emailEstablishment != null && establishmentContact?.emailEstablishment != "") || (establishmentContact?.numberEstablishment != null && establishmentContact?.numberEstablishment != "") || (establishmentContact?.addressEstablishment != null && establishmentContact?.addressEstablishment != "")),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Informations de contact de votre établissement",
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          if (establishmentContact?.emailEstablishment != null && establishmentContact?.emailEstablishment != "")
                            GestureDetector(
                              child: Text(
                                "Adresse email : " + establishmentContact!.emailEstablishment!,
                                style: TextStyle(fontSize: 14),
                              ),
                              onTap: () {
                                _launchMail(establishmentContact!.emailEstablishment!);
                              },
                            ),
                          if (establishmentContact?.numberEstablishment != null && establishmentContact?.numberEstablishment != "")
                            GestureDetector(
                              child: Text(
                                "Numéro de téléphone : " + establishmentContact!.numberEstablishment!,
                                style: TextStyle(fontSize: 14),
                              ),
                              onTap: () {
                                _launchCaller(establishmentContact!.numberEstablishment!);
                              },
                            ),
                          if (establishmentContact?.addressEstablishment != null && establishmentContact?.addressEstablishment != "")
                            Text(
                              "Adresse : " + establishmentContact!.addressEstablishment!,
                              style: TextStyle(fontSize: 14),
                            ),
                        ],
                      ),
                    )
                ),
              ),
              SizedBox(height: 16),
              Visibility(
                visible: establishmentNumbers!.isNotEmpty,
                child: RichText(
                  text: TextSpan(
                    style: DefaultTextStyle.of(context).style,
                    children: <TextSpan>[
                      TextSpan(
                        text: "Votre établissement a mis à disposition certains contacts utiles. ",
                        style: TextStyle(fontSize: 16),
                      ),
                      TextSpan(
                        text: "Vous pouvez cliquer sur chaque numéro pour directement passer un appel.",
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16),
              if (establishmentNumbers != null && establishmentNumbers!.isNotEmpty)
                Container(
                  width: double.infinity,
                  height: 170,
                  child: ListView.builder(
                    itemCount: establishmentNumbers!.length,
                    itemBuilder: (context, index) {
                      final number = establishmentNumbers![index];
                      return ListTile(
                        title: Text(number.name!),
                        subtitle: Text(number.number!.toString()),
                        onTap: () {
                          _launchCaller(number.number!);
                        },
                      );
                    },
                  ),
                ),
            ],
          ),
        )
      ],
    );
  }


  void _fetchUserData() async {
    final storage = new FlutterSecureStorage();
    String? rank = await storage.read(key: 'THERMASSIST_ROLE');
    String? nick = await storage.read(key: 'THERMASSIST_USERNAME');
    String? mail = await storage.read(key: 'THERMASSIST_EMAIL');
    String? name = await storage.read(key: 'THERMASSIST_NAME');
    String? surname = await storage.read(key: 'THERMASSIST_SURNAME');
    setState(() {
      userRank = rank;
      username = nick;
      userMail = mail;
      userName = name;
      userSurname = surname;
    });
  }

  void _fetchEstablishmentData() async {
    try {
      final establishmentService = EstablishmentService();
      final establishmentData = await establishmentService.getCurrentEstablishment();
      setState(() {
        if (establishmentData.contactestablishment != null) {
          establishmentContact = EstablishmentContact.fromJson(jsonDecode(establishmentData.contactestablishment!));
        }
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _fetchSettings() async {
    try {
      final settingsService = SettingsService();
      final settingsData = await settingsService.getCurrentSettings();
      setState(() {
        if (settingsData.numbersettings != null) {
          establishmentNumbers = (jsonDecode(settingsData.numbersettings!) as List)
              .map((item) => EstablishmentNumber.fromJson(item))
              .toList();
        }
        if (settingsData.servicessettings != null) {
          serviceSettings = (jsonDecode(settingsData.servicessettings) as List)
          .map((e) => ServiceConfig.fromJson(e)).toList();
        }
        isLoading = false;
      });
    } catch (error) {
      // Handle error when fetching select options
      print('Error: $error');
    }
  }

  void _openBrowser(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  bool _canDisplay(String key) {
    if (key == "Contact") {
      return (((establishmentContact?.emailEstablishment != null && establishmentContact?.emailEstablishment != "") || (establishmentContact?.numberEstablishment != null && establishmentContact?.numberEstablishment != "") || (establishmentContact?.addressEstablishment != null && establishmentContact?.addressEstablishment != "")) || establishmentNumbers!.isNotEmpty);
    }
    if (serviceSettings == null) return true;
    for (var service in serviceSettings!) {
      print(service.name);
      if (service.name == key) {
        return service.status!;
      }
    }
    // If the service with the given ID is not found, return true by default
    return true;
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      // Show a loading indicator while data is being fetched
      return Center(child: CircularProgressIndicator());
    }
    if (userRank == "USER") {
      return Drawer(
        surfaceTintColor: Colors.white,
        // Add a ListView to the drawer. This ensures the user can scroll
        // through the options in the drawer if there isn't enough vertical
        // space to fit everything.
        child: ListView(
          // Important: Remove any padding from the ListView.
          padding: EdgeInsets.zero,
          children: [
            if (username != null)
              UserAccountsDrawerHeader(
                decoration: const BoxDecoration(
                    color: Color(0xFF0d1825)
                ),
                accountName: Text(username!),
                accountEmail: Text(userMail == null ? 'Aucune adresse email' : userMail!),
                currentAccountPicture: GestureDetector(
                  onTap: () {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(builder: (context) => const ProfilePage()),
                    );
                  },
                  child: CircleAvatar(
                    radius: 60.0,
                    backgroundImage: NetworkImage("https://ui-avatars.com/api/?background=0D8ABC&color=fff&name=" + userSurname! + userName! + "&size=128"),
                  ),
                )
              ),
            ListTile(
                title: const Text('Tableau de bord'),
                leading: Icon(Icons.home),
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => DashboardPage()),
                  );
                },
            ),
            if (_canDisplay("Planning"))
            ListTile(
                title: const Text('Planning'),
                leading: Icon(Icons.today),
                onTap: () {
                    _openBrowser("https://thermassist.fr/user/schedule");
                },
            ),
            ListTile(
                title: const Text('Ordonnances'),
                leading: Icon(Icons.folder),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/documents");
                },
            ),
            if (_canDisplay("Plans"))
            ListTile(
                title: const Text('Plans'),
                leading: Icon(Icons.local_library),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/map");
                },
            ),
            if (_canDisplay("Trombinoscope"))
            ListTile(
                title: const Text('Trombinoscope'),
                leading: Icon(Icons.camera),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/organization");
                },
            ),
            if (_canDisplay("Activités internes"))
            ListTile(
                title: const Text('Activités internes'),
                leading: Icon(Icons.event),
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => InternalEventsPage()),
                  );
                },
            ),
            if (_canDisplay("Activités externes"))
            ListTile(
                title: const Text('Activités externes'),
                leading: Icon(Icons.directions_bike),
              onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => ExternalEventsPage()),
                );
              },
            ),
            if (_canDisplay("Offres partenaires"))
            ListTile(
                title: const Text('Offres partenaires'),
                leading: Icon(Icons.storefront),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/partners");
                },
            ),
            if (_canDisplay("Paiement en ligne") && _canDisplay("Activités internes"))
              ListTile(
                title: const Text('Réservations'),
                leading: Icon(Icons.confirmation_num),
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => ReservationsPage()),
                  );
                },
              ),
            if (_canDisplay("Boutique"))
            ListTile(
                title: const Text('Boutique'),
                leading: Icon(Icons.shopping_bag),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/shop");
                },
            ),
            if (_canDisplay("Boutique"))
            ListTile(
                title: const Text('Commandes'),
                leading: Icon(Icons.shopping_cart),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/orders");
                },
            ),
            if (_canDisplay("Questionnaire"))
            ListTile(
                title: const Text('Questionnaire'),
                leading: Icon(Icons.edit_note),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/form");
                },
            ),
            if (_canDisplay("Contact"))
            ListTile(
                title: const Text('Contact'),
                leading: Icon(Icons.contacts),
                onTap: () {
                  _showContactBottomSheet(context);
                },
            ),
            ListTile(
                title: const Text('FAQ'),
                leading: Icon(Icons.contact_support),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/faq");
                },
            ),
            ListTile(
                title: const Text('Politique de confidentialité'),
                leading: Icon(Icons.contact_support),
                onTap: () {
                  _openBrowser("https://thermassist.fr/user/policy");
                },
            ),
            ListTile(
              title: const Text('Déconnexion'),
              leading: Icon(Icons.exit_to_app),
              onTap: () async {
                // Perform the disconnect logic
                final storage = new FlutterSecureStorage();
                await storage.delete(key: 'THERMASSIST_TOKEN');

                // Navigate to the login page
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => WelcomePage()),
                );
              },
            ),
          ],
        ),
      );
    } else {
      return Drawer(
        // Add a ListView to the drawer. This ensures the user can scroll
        // through the options in the drawer if there isn't enough vertical
        // space to fit everything.
        child: ListView(
          // Important: Remove any padding from the ListView.
          padding: EdgeInsets.zero,
          children: [
            if (username != null)
              UserAccountsDrawerHeader(
                decoration: const BoxDecoration(
                    color: Color(0xFF0d1825)
                ),
                accountName: Text(username!),
                accountEmail: Text(userMail == null ? 'Aucune adresse email' : userMail!),
                currentAccountPicture: CircleAvatar(
                  radius: 60.0,
                  backgroundImage: NetworkImage("https://ui-avatars.com/api/?background=0D8ABC&color=fff&name=${userSurname}${userName}"),
                ),
              ),
            ListTile(
                title: const Text('Tableau de bord'),
                leading: Icon(Icons.home),
              onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => DashboardPage()),
                );
              },
            ),
            if (_canDisplay("Plans"))
            ListTile(
              title: const Text('Plans'),
              leading: Icon(Icons.local_library),
              onTap: () {
                _openBrowser("https://thermassist.fr/user/map");
              },
            ),
            if (_canDisplay("Activités externes"))
            ListTile(
                title: const Text('Activités touristiques'),
                leading: Icon(Icons.directions_bike),
              onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => ExternalEventsPage()),
                );
              },
            ),
            if (_canDisplay("Offres partenaires"))
            ListTile(
              title: const Text('Offres partenaires'),
              leading: Icon(Icons.storefront),
              onTap: () {
                _openBrowser("https://thermassist.fr/user/partners");
              },
            ),
            if (_canDisplay("Activités internes"))
              ListTile(
                title: const Text('Offres Séjour'),
                leading: Icon(Icons.event),
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => InternalEventsPage()),
                  );
                },
              ),
            if (_canDisplay("Paiement en ligne") && _canDisplay("Activités internes"))
              ListTile(
                title: const Text('Réservations'),
                leading: Icon(Icons.confirmation_num),
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => ReservationsPage()),
                  );
                },
              ),
            if (_canDisplay("Boutique"))
            ListTile(
              title: const Text('Boutique'),
              leading: Icon(Icons.shopping_bag),
              onTap: () {
                _openBrowser("https://thermassist.fr/user/shop");
              },
            ),
            if (_canDisplay("Boutique"))
            ListTile(
              title: const Text('Commandes'),
              leading: Icon(Icons.shopping_cart),
              onTap: () {
                _openBrowser("https://thermassist.fr/user/orders");
              },
            ),
            if (_canDisplay("Questionnaire"))
            ListTile(
              title: const Text('Questionnaire'),
              leading: Icon(Icons.edit_note),
              onTap: () {
                _openBrowser("https://thermassist.fr/user/form");
              },
            ),
            if (_canDisplay("Contact"))
            ListTile(
              title: const Text('Contact'),
              leading: Icon(Icons.contacts),
              onTap: () {
                _showContactBottomSheet(context);
              },
            ),
            ListTile(
              title: const Text('Déconnexion'),
              leading: Icon(Icons.exit_to_app),
              onTap: () async {
                // Perform the disconnect logic
                final storage = new FlutterSecureStorage();
                await storage.delete(key: 'THERMASSIST_TOKEN');

                // Navigate to the login page
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => WelcomePage()),
                );
              },
            ),
          ],
        ),
      );
    }

  }
}