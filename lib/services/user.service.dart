import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/knowledge.model.dart';
import 'package:thermassist_mobile/models/remaining-days.model.dart';
import 'package:thermassist_mobile/models/user-preferences.model.dart';

import '../config/config.dart';

class UserService {
  final Dio _dio = Dio();

  Future<RemainingDays> getRemainingDays() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/user/stay/remaining',
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        final RemainingDays remainingDays = RemainingDays.fromJson(data);
        return remainingDays;
      } else {
        throw Exception('Failed to load remaining days for current user.');
      }
    } catch (error) {
      throw Exception('Error fetching remaining days for current user: $error');
    }
  }

  Future<void> updatePreferences(UserPreferences userPreferences) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    final jsonData = {
      'preferences': jsonEncode(userPreferences.toJson())
    };
    try {
      final response = await _dio.post('${AppConfig.baseUrl}/user/preferences',
          data: jsonData,
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode != 200) {
        throw Exception('Failed to update preferences for current user.');
      }
    } catch (error) {
      throw Exception('Error updating preferences for current user: $error');
    }
  }

  Future<void> updateFeeds(List<String> feeds) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    final jsonData = {
      'feeds': jsonEncode(feeds)
    };
    try {
      final response = await _dio.post('${AppConfig.baseUrl}/user/feeds',
          data: jsonData,
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode != 200) {
        throw Exception('Failed to update feeds for current user.');
      }
    } catch (error) {
      throw Exception('Error updating feeds for current user: $error');
    }
  }

  Future<void> deleteAccount(String password) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    final jsonData = {
      'password': password
    };
    try {
      final response = await _dio.post('${AppConfig.baseUrl}/user',
          data: jsonData,
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode != 200) {
        throw Exception('Failed to delete account for current user.');
      }
    } catch (error) {
      throw Exception('Error deleting account for current user: $error');
    }
  }

  Future<void> updatePassword(String oldPassword, String newPassword) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    final jsonData = {
      'oldPassword': oldPassword,
      'newPassword': newPassword
    };
    try {
      final response = await _dio.put('${AppConfig.baseUrl}/user/password',
          data: jsonData,
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode != 200) {
        throw Exception('Failed to update password for current user.');
      }
    } catch (error) {
      throw Exception('Error updating password for current user: $error');
    }
  }

  Future<void> updateFcmToken() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    String? fcm = await storage.read(key: 'FCM_TOKEN');
    final jsonData = {
      'fcmToken': fcm
    };
    try {
      final response = await _dio.put('${AppConfig.baseUrl}/user/fcm/token',
          data: jsonData,
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode != 200) {
        throw Exception('Failed to update fcm token for current user.');
      }
    } catch (error) {
      throw Exception('Error updating fcm token for current user: $error');
    }
  }

}