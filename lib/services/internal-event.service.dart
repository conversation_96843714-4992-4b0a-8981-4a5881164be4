import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/models/internal-event-status.model.dart';
import 'package:thermassist_mobile/models/internal-event.model.dart';
import 'package:thermassist_mobile/models/station.model.dart';

import '../config/config.dart';

class InternalEventService {
  final Dio _dio = Dio();

  Future<List<InternalEvent>> getCurrentInternalEvents() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/event/internal/current',
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          )
      ); // Replace with your API endpoint
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<InternalEvent> events = data.map((item) => InternalEvent.fromJson(item)).toList();
        return events;
      } else {
        throw Exception('Failed to load stations.');
      }
    } catch (error) {
      throw Exception('Error fetching stations: $error');
    }
  }

  Future<InternalEventStatus> getInternalEventStatus(int internalEventId) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/event/internal/status/' + internalEventId.toString(),
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          )
      ); // Replace with your API endpoint
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        final InternalEventStatus status = InternalEventStatus.fromJson(data);
        return status;
      } else {
        throw Exception('Failed to load internal event status.');
      }
    } catch (error) {
      throw Exception('Error fetching internal event status: $error');
    }
  }
}