import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';

import '../config/config.dart';

class EstablishmentService {
  final Dio _dio = Dio();

  Future<List<Establishment>> getPublicEstablishments() async {
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/establishment/public');
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<Establishment> options = data.map((item) => Establishment.fromJson(item)).toList();
        return options;
      } else {
        throw Exception('Failed to load public establishments.');
      }
    } catch (error) {
      throw Exception('Error fetching public establishments: $error');
    }
  }

  Future<List<Establishment>> getEstablishmentsForStation(int stationId) async {
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/establishment/public/station/$stationId');
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<Establishment> options = data.map((item) => Establishment.fromJson(item)).toList();
        return options;
      } else {
        throw Exception('Failed to load establishments for station $stationId.');
      }
    } catch (error) {
      throw Exception('Error fetching establishments for station $stationId: $error');
    }
  }

  Future<Establishment> getCurrentEstablishment() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/establishment/current',
        options: Options(
            headers: {
              "authorization": "Bearer $token"
            }
        ));
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        final Establishment establishment = Establishment.fromJson(data);
        return establishment;
      } else {
        throw Exception('Failed to load current establishment data.');
      }
    } catch (error) {
      throw Exception('Error fetching current establishment data: $error');
    }
  }
}