import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/user.model.dart';

import '../config/config.dart';

class AuthService {
  final Dio _dio = Dio();

  Future<User> whoAmI() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/auth/whoami',
      options: Options(
        headers: {
          "authorization": "Bearer $token"
        }
      ));
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        final User user = User.fromJson(data);
        return user;
      } else {
        throw Exception('Failed to load user data.');
      }
    } catch (error) {
      throw Exception('Error fetching user data: $error');
    }
  }
}