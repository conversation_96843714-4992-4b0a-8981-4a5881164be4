import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/knowledge.model.dart';

import '../config/config.dart';

class KnowledgeService {
  final Dio _dio = Dio();

  Future<List<Knowledge>> getCurrentKnowledge() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/knowledge/current',
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<Knowledge> knowledge = data.map((item) => Knowledge.fromJson(item)).toList();
        return knowledge;
      } else {
        throw Exception('Failed to load knowledge for current user.');
      }
    } catch (error) {
      throw Exception('Error fetching knowledge for current user: $error');
    }
  }

}