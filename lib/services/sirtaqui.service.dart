import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/models/feed.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-feed.model.dart';
import 'package:thermassist_mobile/models/station.model.dart';

import '../config/config.dart';

class SirtaquiService {
  final Dio _dio = Dio();

  Future<List<Feed>> getFeeds() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/sirtaqui/events/types',
      options: Options(
          headers: {
            "authorization": "Bearer $token"
          }
      ));
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<Feed> feeds = data.map((item) => Feed.fromJson(item)).toList();
        return feeds;
      } else {
        throw Exception('Failed to load feeds.');
      }
    } catch (error) {
      throw Exception('Error fetching feeds: $error');
    }
  }

  Future<SirtaquiFeed> getFeedEvents(String feedName) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/sirtaqui/events/$feedName',
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        return SirtaquiFeed.fromJson(data);
      } else {
        throw Exception('Failed to load feed $feedName.');
      }
    } catch (error) {
      throw Exception('Error fetching feed $feedName: $error');
    }
  }
}