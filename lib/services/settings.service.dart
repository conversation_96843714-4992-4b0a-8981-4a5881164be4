import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/models/settings.model.dart';

import '../config/config.dart';

class SettingsService {
  final Dio _dio = Dio();

  Future<Settings> getCurrentSettings() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/settings/current',
      options: Options(
          headers: {
            "authorization": "Bearer $token"
          }
      ));
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        final Settings settings = Settings.fromJson(data);
        return settings;
      } else {
        throw Exception('Failed to load establishments for current user.');
      }
    } catch (error) {
      throw Exception('Error fetching establishments for current user: $error');
    }
  }

}