import 'package:dio/dio.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/models/station.model.dart';

import '../config/config.dart';

class StationService {
  final Dio _dio = Dio();

  Future<List<Station>> getStations() async {
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/station/registerable'); // Replace with your API endpoint
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<Station> options = data.map((item) => Station.fromJson(item)).toList();
        return options;
      } else {
        throw Exception('Failed to load stations.');
      }
    } catch (error) {
      throw Exception('Error fetching stations: $error');
    }
  }
}