import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/models/internal-event.model.dart';
import 'package:thermassist_mobile/models/reservation.model.dart';
import 'package:thermassist_mobile/models/station.model.dart';

import '../config/config.dart';
import '../models/basket-item.model.dart';

class ReservationService {
  final Dio _dio = Dio();

  Future<List<Reservation>> getCurrentReservations() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/event/internal/reservations',
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          )
      ); // Replace with your API endpoint
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<Reservation> reservations = data.map((item) => Reservation.fromJson(item)).toList();
        return reservations;
      } else {
        throw Exception('Failed to load reservations.');
      }
    } catch (error) {
      throw Exception('Error fetching reservations: $error');
    }
  }

  Future<void> cancelReservation(int internalEventId) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.delete('${AppConfig.baseUrl}/event/internal/' + internalEventId.toString() + '/reservation/cancel',
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          )
      ); // Replace with your API endpoint
      if (response.statusCode == 200) {
        return;
      } else {
        throw Exception('Failed to cancel reservation.');
      }
    } catch (error) {
      throw Exception('Error fetching reservation: $error');
    }
  }

  Future<void> registerReservationOnSite(int internalEventId, int pricingIndex) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.post('${AppConfig.baseUrl}/event/internal/register/' + internalEventId.toString(),
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ),
        data: {
          "pricingIndex": pricingIndex
        }
      ); // Replace with your API endpoint
      if (response.statusCode == 200) {
        return;
      } else {
        throw Exception('Failed to register for reservation.');
      }
    } catch (error) {
      throw Exception('Error registering for reservation: $error');
    }
  }

  Future<String> getStripeReceiptLink(int reservationId) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/payment/receipt/reservation/' + reservationId.toString(),
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          )
      ); // Replace with your API endpoint
      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load reservation receipt.');
      }
    } catch (error) {
      throw Exception('Error fetching reservation receipt: $error');
    }
  }

  Future<String> payForReservation(List<BasketItem> basketItems) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final jsonBasketItems = basketItems.map((item) => item.toJson()).toList();
      final response = await _dio.post('${AppConfig.baseUrl}/event/internal/pay/mobile',
          data: {
            "cartItems": jsonBasketItems
          },
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ),
      ); // Replace with your API endpoint
      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load reservation receipt.');
      }
    } catch (error) {
      throw Exception('Error fetching reservation receipt: $error');
    }
  }
}