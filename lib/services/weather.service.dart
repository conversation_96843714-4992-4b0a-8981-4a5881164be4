import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/knowledge.model.dart';
import 'package:thermassist_mobile/models/remaining-days.model.dart';
import 'package:thermassist_mobile/models/weather-forecast.model.dart';
import 'package:thermassist_mobile/models/weather.model.dart';

import '../config/config.dart';

class WeatherService {
  final Dio _dio = Dio();

  Future<Weather> getWeather(double latitude, double longitude) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      Map<String, dynamic> postData = {
        'latitude': latitude,
        'longitude': longitude,
      };
      final response = await _dio.post('${AppConfig.baseUrl}/weather/current',
          data: postData,
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        final Weather weather = Weather.fromJson(data);
        return weather;
      } else {
        throw Exception('Failed to load weather for current user.');
      }
    } catch (error) {
      throw Exception('Error fetching weather for current user: $error');
    }
  }

  Future<WeatherForecast> getForecast(double latitude, double longitude) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      Map<String, dynamic> postData = {
        'latitude': latitude,
        'longitude': longitude,
      };
      final response = await _dio.post('${AppConfig.baseUrl}/weather/forecast',
          data: postData,
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        final WeatherForecast forecast = WeatherForecast.fromJson(data);
        return forecast;
      } else {
        throw Exception('Failed to load forecast for current user.');
      }
    } catch (error) {
      throw Exception('Error fetching forecast for current user: $error');
    }
  }

}