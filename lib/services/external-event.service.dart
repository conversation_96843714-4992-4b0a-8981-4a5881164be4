import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/external-event.model.dart';
import 'package:thermassist_mobile/models/internal-event.model.dart';

import '../config/config.dart';

class ExternalEventService {
  final Dio _dio = Dio();

  Future<List<ExternalEvent>> getCurrentExternalEvents() async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      final response = await _dio.get('${AppConfig.baseUrl}/event/external/current',
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          )
      ); // Replace with your API endpoint
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<ExternalEvent> events = data.map((item) => ExternalEvent.fromJson(item)).toList();
        return events;
      } else {
        throw Exception('Failed to load stations.');
      }
    } catch (error) {
      throw Exception('Error fetching stations: $error');
    }
  }
}