import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:thermassist_mobile/models/knowledge.model.dart';
import 'package:thermassist_mobile/models/marea.model.dart';
import 'package:thermassist_mobile/models/remaining-days.model.dart';
import 'package:thermassist_mobile/models/weather-forecast.model.dart';
import 'package:thermassist_mobile/models/weather.model.dart';

import '../config/config.dart';

class MareaService {
  final Dio _dio = Dio();

  Future<Marea> getTides(double latitude, double longitude) async {
    final storage = new FlutterSecureStorage();
    String? token = await storage.read(key: 'THERMASSIST_TOKEN');
    try {
      Map<String, dynamic> postData = {
        'latitude': latitude,
        'longitude': longitude,
      };
      final response = await _dio.post('${AppConfig.baseUrl}/marea/tides',
          data: postData,
          options: Options(
              headers: {
                "authorization": "Bearer $token"
              }
          ));
      if (response.statusCode == 200) {
        final dynamic data = response.data;
        final Marea tides = Marea.from<PERSON>son(data);
        return tides;
      } else {
        throw Exception('Failed to load tides data for current user.');
      }
    } catch (error) {
      throw Exception('Error fetching tides data for current user: $error');
    }
  }

}