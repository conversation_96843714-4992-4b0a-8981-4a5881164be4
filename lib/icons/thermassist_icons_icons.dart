/// Flutter icons ThermassistIcons
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  ThermassistIcons
///      fonts:
///       - asset: fonts/ThermassistIcons.ttf
///
/// 
///
import 'package:flutter/widgets.dart';

class ThermassistIcons {
  ThermassistIcons._();

  static const _kFontFam = 'ThermassistIcons';
  static const String? _kFontPkg = null;

  static const IconData massage = IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
