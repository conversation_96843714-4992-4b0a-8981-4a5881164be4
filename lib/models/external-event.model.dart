class ExternalEvent {
  final int idexternalevent;
  final String summaryexternalevent;
  final String titleexternalevent;
  final String descexternalevent;
  final String? picexternalevent;
  final String? linkexternalevent;

  ExternalEvent({required this.idexternalevent, required this.summaryexternalevent, required this.titleexternalevent, required this.descexternalevent, required this.picexternalevent, required this.linkexternalevent});

  factory ExternalEvent.fromJson(Map<String, dynamic> json) {
    return ExternalEvent(
        idexternalevent: json['idexternalevent'],
        summaryexternalevent: json['summaryexternalevent'],
        titleexternalevent: json['titleexternalevent'],
        descexternalevent: json['descexternalevent'],
        picexternalevent: json['picexternalevent'],
        linkexternalevent: json['linkexternalevent']
    );
  }
}