import 'package:thermassist_mobile/models/internal-event.model.dart';

class BasketItem {
  final int internalEventId;
  final int pricingId;

  BasketItem({ required this.internalEventId, required this.pricingId });

  Map<String, dynamic> toJson() {
    return {
      'internalEventId': internalEventId,
      'pricingId': pricingId,
    };
  }

  factory BasketItem.fromJson(Map<String, dynamic> json) {
    return BasketItem(
      internalEventId: json['internalEventId'],
      pricingId: json['pricingId'],
    );
  }
}