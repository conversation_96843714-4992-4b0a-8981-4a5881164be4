class InternalEvent {
  final int idinternalevent;
  final String summaryinternalevent;
  final String titleinternalevent;
  final String descinternalevent;
  final String? picinternalevent;
  final String? linkinternalevent;
  final int? minusersinternalevent;
  final int? maxusersinternalevent;
  final String? nextdateinternalevent;
  final int? daysbeforelimitinternalevent;
  final String? paymentmodeinternalevent;
  final String? addressinternalevent;
  final String? contactinternalevent;
  final int? approvedinternalevent;
  final String? stripeproductid;
  final String? dateinternalevent;
  final String? pricesinternalevent;
  final String? triggersinternalevent;
  final String? frequencyinternalevent;

  InternalEvent(
    {
      required this.idinternalevent,
      required this.summaryinternalevent,
      required this.titleinternalevent,
      required this.descinternalevent,
      required this.picinternalevent,
      required this.linkinternalevent,
      this.minusersinternalevent,
      this.maxusersinternalevent,
      this.nextdateinternalevent,
      this.daysbeforelimitinternalevent,
      this.paymentmodeinternalevent,
      this.addressinternalevent,
      this.contactinternalevent,
      this.approvedinternalevent,
      this.stripeproductid,
      this.dateinternalevent,
      this.pricesinternalevent,
      this.triggersinternalevent,
      this.frequencyinternalevent
    }
  );

  factory InternalEvent.fromJson(Map<String, dynamic> json) {
    return InternalEvent(
        idinternalevent: json['idinternalevent'],
        summaryinternalevent: json['summaryinternalevent'],
        titleinternalevent: json['titleinternalevent'],
        descinternalevent: json['descinternalevent'],
        picinternalevent: json['picinternalevent'],
        linkinternalevent: json['linkinternalevent'],
        minusersinternalevent: json['minusersinternalevent'],
        maxusersinternalevent: json['maxusersinternalevent'],
        nextdateinternalevent: json['nextdateinternalevent'],
        daysbeforelimitinternalevent: json['daysbeforelimitinternalevent'],
        paymentmodeinternalevent: json['paymentmodeinternalevent'],
        addressinternalevent: json['addressinternalevent'],
        contactinternalevent: json['contactinternalevent'],
        approvedinternalevent: json['approvedinternalevent'],
        stripeproductid: json['stripeproductid'],
        dateinternalevent: json['dateinternalevent'],
        pricesinternalevent: json['pricesinternalevent'],
        triggersinternalevent: json['triggersinternalevent'],
        frequencyinternalevent: json['frequencyinternalevent']
    );
  }
}