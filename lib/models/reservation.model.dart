import 'package:thermassist_mobile/models/internal-event.model.dart';
import 'package:thermassist_mobile/models/user.model.dart';

class Reservation {
  final int reservationid;
  final int internaleventid;
  final int userid;
  final bool paid;
  final String reservationdate;
  final String eventdate;
  final double reservationprice;
  final String? stripeintentid;
  final String reservationtype;
  final InternalEvent internalEvent;
  final User user;

  Reservation({required this.reservationid, required this.internaleventid, required this.userid, required this.paid, required this.reservationdate, required this.eventdate, required this.reservationprice, this.stripeintentid, required this.reservationtype, required this.internalEvent, required this.user});

  factory Reservation.fromJson(Map<String, dynamic> json) {
    return Reservation(
        reservationid: json['reservationid'],
        internaleventid: json['internaleventid'],
        userid: json['userid'],
        paid: json['paid'] is bool ? json['paid'] : json['paid'] == 'true',
        reservationdate: json['reservationdate'],
        eventdate: json['eventdate'],
        reservationprice: json['reservationprice'],
        stripeintentid: json['stripeintentid'] != null ? json['stripeintentid'] : null,
        reservationtype: json['reservationtype'],
        internalEvent: InternalEvent.fromJson(json['internalEvent']),
        user: User.fromJson(json['user']),
    );
  }
}