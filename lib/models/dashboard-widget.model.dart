class DashboardWidget {
  final String codewidget;
  final int defaultxwidget;
  final int defaultywidget;
  final int idwidget;
  final String imagewidget;
  final int minxwidget;
  final int minywidget;
  final String readablewidget;
  final dynamic extra;

  DashboardWidget({required this.codewidget, required this.defaultxwidget, required this.defaultywidget, required this.idwidget, required this.imagewidget, required this.minxwidget, required this.minywidget, required this.readablewidget, required this.extra});

  factory DashboardWidget.fromJson(Map<String, dynamic> json) {
    return DashboardWidget(
        codewidget: json['codewidget'],
        defaultxwidget: json['defaultxwidget'],
        defaultywidget: json['defaultywidget'],
        idwidget: json['idwidget'],
        imagewidget: json['imagewidget'],
        minxwidget: json['minxwidget'],
        minywidget: json['minywidget'],
        readablewidget: json['readablewidget'],
        extra: json['extra'],
    );
  }
}