import 'coordinates.model.dart';

class WeatherLocation {
  final int id;
  final String name;
  final Coordinates coordinate;

  WeatherLocation({required this.id, required this.name, required this.coordinate});

  factory WeatherLocation.fromJson(Map<String, dynamic> json) {
    return WeatherLocation(
        id: json['id'],
        name: json['name'],
        coordinate: Coordinates.from<PERSON>son(json['coordinate'])
    );
  }
}