import 'package:thermassist_mobile/models/weather-forecast-item.model.dart';

class WeatherForecast {
  final List<WeatherForecastItem> weatherForecasts;

  WeatherForecast({required this.weatherForecasts});

  factory WeatherForecast.fromJson(Map<String, dynamic> json) {
    final List<dynamic> forecastList = json['weatherForecasts'];
    final List<WeatherForecastItem> forecasts = forecastList
        .map((forecastData) => WeatherForecastItem.fromJson(forecastData))
        .toList();
    return WeatherForecast(
        weatherForecasts: forecasts
    );
  }
}