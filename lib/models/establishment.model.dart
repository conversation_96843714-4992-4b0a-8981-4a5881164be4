import 'package:thermassist_mobile/models/station.model.dart';

class Establishment {
  final int idestablishment;
  final String nameestablishment;
  final Station? station;
  final String? areaestablishment;
  final String? contactestablishment;

  Establishment({required this.idestablishment, required this.nameestablishment, required this.station, required this.areaestablishment, required this.contactestablishment});

  factory Establishment.fromJson(Map<String, dynamic> json) {
    return Establishment(
      idestablishment: json['idestablishment'],
      nameestablishment: json['nameestablishment'],
      station: json['station'] == null ? null : Station.fromJson(json['station']),
      areaestablishment: json['areaestablishment'],
      contactestablishment: json['contactestablishment'],
    );
  }
}