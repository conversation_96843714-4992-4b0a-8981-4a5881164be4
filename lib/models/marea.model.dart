import 'package:thermassist_mobile/models/marea-extreme.model.dart';
import 'package:thermassist_mobile/models/marea-height.model.dart';

class Marea {
  final List<MareaExtreme> extremes;
  final List<MareaHeight> heights;

  Marea({required this.extremes, required this.heights});

  factory Marea.fromJson(Map<String, dynamic> json) {
    final List<dynamic> extremesList = json['extremes'];
    final List<MareaExtreme> extremes = extremesList
        .map((extremeData) => MareaExtreme.fromJson(extremeData))
        .toList();

    final List<dynamic> heightsList = json['heights'];
    final List<MareaHeight> heights = heightsList
        .map((heightData) => MareaHeight.fromJson(heightData))
        .toList();

    return Marea(
        extremes: extremes,
        heights: heights
    );
  }
}