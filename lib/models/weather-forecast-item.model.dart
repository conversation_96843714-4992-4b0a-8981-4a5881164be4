import 'package:thermassist_mobile/models/weather-location.model.dart';
import 'package:thermassist_mobile/models/weather-state.model.dart';
import 'package:thermassist_mobile/models/weather-temperature.model.dart';

class WeatherForecastItem {
  final WeatherTemperature temperature;
  final WeatherState weatherState;
  final String forecastTime;

  WeatherForecastItem({required this.temperature, required this.weatherState, required this.forecastTime});

  factory WeatherForecastItem.fromJson(Map<String, dynamic> json) {
    return WeatherForecastItem(
        temperature: WeatherTemperature.fromJson(json['temperature']),
        weatherState: WeatherState.fromJson(json['weatherState']),
        forecastTime: json['forecastTime']
    );
  }
}