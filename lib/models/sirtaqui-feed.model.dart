import 'package:thermassist_mobile/models/sirtaqui-event.model.dart';

class SirtaquiFeed {
  final String tisTrackingUA;
  final List<SirtaquiEvent> value;

  SirtaquiFeed({required this.tisTrackingUA, required this.value});

  factory SirtaquiFeed.fromJson(Map<String, dynamic> json) {

    final List<dynamic> sirtaquiEventList = json['value'];
    final List<SirtaquiEvent> sirtaquiEvents = sirtaquiEventList
        .map((sirtaquiEventData) => SirtaquiEvent.fromJson(sirtaquiEventData))
        .toList();

    return SirtaquiFeed(
      tisTrackingUA: json['tisTrackingUA'],
      value: sirtaquiEvents,
    );
  }
}