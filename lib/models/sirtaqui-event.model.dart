import 'package:thermassist_mobile/models/sirtaqui-address.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-communication.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-dates.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-description.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-document.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-photo.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-schedule.model.dart';
import 'package:thermassist_mobile/models/sirtaqui-zone.model.dart';

class SirtaquiEvent {
  final String? SyndicObjectName;
  final String? DESCRIPTIFACTIVITES;
  final String? DESCRIPTIFGEN;
  final List<SirtaquiAddress>? ADRESSEs;
  final List<SirtaquiDescription>? DESCRIPTIFSs;
  final List<SirtaquiCommunication>? MOYENSCOMs;
  final List<SirtaquiPhoto>? PHOTOSs;
  final List<SirtaquiSchedule>? OUVERTUREs;
  final List<SirtaquiDates>? VALIDITEDATESs;
  final List<SirtaquiDocument>? PLAQUETTESTRACESs;
  final List<SirtaquiZone>? ZONESs;

  SirtaquiEvent({required this.SyndicObjectName, required this.DESCRIPTIFACTIVITES, required this.DESCRIPTIFGEN, required this.ADRESSEs, required this.DESCRIPTIFSs, required this.MOYENSCOMs, required this.PHOTOSs, required this.OUVERTUREs, required this.VALIDITEDATESs, required this.PLAQUETTESTRACESs, required this.ZONESs});

  factory SirtaquiEvent.fromJson(Map<String, dynamic> json) {

    List<SirtaquiAddress>? address;
    if (json['ADRESSEs'] != null) {
      final List<dynamic> addressList = json['ADRESSEs'];
      address = addressList
          .map((addressData) => SirtaquiAddress.fromJson(addressData))
          .toList();
    } else {
      address = null;
    }

    List<SirtaquiDescription>? description;
    if (json['DESCRIPTIFSs'] != null) {
      final List<dynamic> descriptionList = json['DESCRIPTIFSs'];
      description = descriptionList
          .map((descriptionData) => SirtaquiDescription.fromJson(descriptionData))
          .toList();
    } else {
      description = null;
    }

    List<SirtaquiCommunication>? communication;
    if (json['MOYENSCOMs'] != null) {
      final List<dynamic> communicationList = json['MOYENSCOMs'];
      communication = communicationList
          .map((communicationData) => SirtaquiCommunication.fromJson(communicationData))
          .toList();
    } else {
      communication = null;
    }

    List<SirtaquiPhoto>? photo;
    if (json['PHOTOSs'] != null) {
      final List<dynamic> photoList = json['PHOTOSs'];
      photo = photoList
          .map((photoData) => SirtaquiPhoto.fromJson(photoData))
          .toList();
    } else {
      photo = null;
    }

    List<SirtaquiSchedule>? schedule;
    if (json['OUVERTUREs'] != null) {
      final List<dynamic> scheduleList = json['OUVERTUREs'];
      schedule = scheduleList
          .map((scheduleData) => SirtaquiSchedule.fromJson(scheduleData))
          .toList();
    } else {
      schedule = null;
    }

    List<SirtaquiDates>? dates;
    if (json['VALIDITEDATESs'] != null) {
      final List<dynamic> datesList = json['VALIDITEDATESs'];
      dates = datesList
          .map((datesData) => SirtaquiDates.fromJson(datesData))
          .toList();
    } else {
      dates = null;
    }

    List<SirtaquiDocument>? documents;
    if (json['PLAQUETTESTRACESs'] != null) {
      final List<dynamic> documentsList = json['PLAQUETTESTRACESs'];
      documents = documentsList
          .map((documentsData) => SirtaquiDocument.fromJson(documentsData))
          .toList();
    } else {
      documents = null;
    }

    List<SirtaquiZone>? zones;
    if (json['ZONESs'] != null) {
      final List<dynamic> zonesList = json['ZONESs'];
      zones = zonesList
          .map((zonesData) => SirtaquiZone.fromJson(zonesData))
          .toList();
    } else {
      zones = null;
    }

    return SirtaquiEvent(
      SyndicObjectName: json['SyndicObjectName'],
      DESCRIPTIFACTIVITES: json['DESCRIPTIFACTIVITES'],
      DESCRIPTIFGEN: json['DESCRIPTIFGEN'],
      ADRESSEs: address,
      DESCRIPTIFSs: description,
      MOYENSCOMs: communication,
      PHOTOSs: photo,
      OUVERTUREs: schedule,
      VALIDITEDATESs: dates,
      PLAQUETTESTRACESs: documents,
      ZONESs: zones,
    );
  }
}