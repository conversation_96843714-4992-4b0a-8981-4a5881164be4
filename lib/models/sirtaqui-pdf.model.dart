class SirtaquiPDF {
  final String? mediaId;
  final String? titre;
  final String? credit;
  final String? url;

  SirtaquiPDF({required this.mediaId, required this.titre, required this.credit, required this.url});

  factory SirtaquiPDF.fromJson(Map<String, dynamic> json) {
    return SirtaquiPDF(
      mediaId: json['mediaId'],
      titre: json['titre'],
      credit: json['credit'],
      url: json['url'],
    );
  }
}