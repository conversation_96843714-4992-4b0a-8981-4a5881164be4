import 'package:thermassist_mobile/models/weather-location.model.dart';
import 'package:thermassist_mobile/models/weather-state.model.dart';
import 'package:thermassist_mobile/models/weather-temperature.model.dart';

class Weather {
  final WeatherLocation location;
  final WeatherTemperature temperature;
  final WeatherState weatherState;

  Weather({required this.location, required this.temperature, required this.weatherState});

  factory Weather.fromJson(Map<String, dynamic> json) {
    return Weather(
        location: WeatherLocation.fromJson(json['location']),
        temperature: WeatherTemperature.fromJson(json['temperature']),
        weatherState: WeatherState.fromJson(json['weatherState'])
    );
  }
}