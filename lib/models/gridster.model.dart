import 'dart:convert';

import 'package:thermassist_mobile/models/dashboard-widget.model.dart';

class Gridster {
  final DashboardWidget widget;
  final int x;
  final int y;
  final int rows;
  final int cols;
  final int minItemCols;
  final int minItemRows;

  Gridster({required this.widget, required this.x, required this.y, required this.rows, required this.cols, required this.minItemCols, required this.minItemRows});

  factory Gridster.fromJson(Map<String, dynamic> jsonObject) {
    return Gridster(
        widget: DashboardWidget.fromJson(jsonObject['widget']),
        x: jsonObject['x'],
        y: jsonObject['y'],
        rows: jsonObject['rows'],
        cols: jsonObject['cols'],
        minItemCols: jsonObject['minItemCols'],
        minItemRows: jsonObject['minItemRows']
    );
  }
}