class EstablishmentContact {
  final String? emailEstablishment;
  final String? numberEstablishment;
  final String? addressEstablishment;

  EstablishmentContact({this.emailEstablishment, this.numberEstablishment, this.addressEstablishment});

  factory EstablishmentContact.fromJson(Map<String, dynamic> json) {
    return EstablishmentContact(
        emailEstablishment: json['emailEstablishment'],
        numberEstablishment: json['numberEstablishment'],
        addressEstablishment: json['addressEstablishment']
    );
  }
}