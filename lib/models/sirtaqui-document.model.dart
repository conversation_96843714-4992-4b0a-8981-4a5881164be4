import 'package:thermassist_mobile/models/sirtaqui-pdf.model.dart';

class SirtaquiDocument {
  final String? id;
  final String? ordre;
  final SirtaquiPDF? fichePDFFR;

  SirtaquiDocument({required this.id, required this.ordre, required this.fichePDFFR});

  factory SirtaquiDocument.fromJson(Map<String, dynamic> json) {
    return SirtaquiDocument(
      id: json['id'],
      ordre: json['ordre'],
      fichePDFFR: json['fichePDFFR'] == null ? null : SirtaquiPDF.fromJson(json['fichePDFFR'])
    );
  }
}