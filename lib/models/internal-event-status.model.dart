class InternalEventStatus {
  final int participants;
  final bool registered;

  InternalEventStatus({ required this.participants, required this.registered });

  Map<String, dynamic> toJson() {
    return {
      'participants': participants,
      'registered': registered,
    };
  }

  factory InternalEventStatus.fromJson(Map<String, dynamic> json) {
    return InternalEventStatus(
      participants: json['participants'],
      registered: json['registered'],
    );
  }
}