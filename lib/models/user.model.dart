import 'dart:convert';

import 'package:thermassist_mobile/models/establishment.model.dart';
import 'package:thermassist_mobile/models/rank.model.dart';
import 'package:thermassist_mobile/models/user-preferences.model.dart';

class User {
  final int iduser;
  final String nickuser;
  final String emailuser;
  final String? phoneuser;
  final String prenomuser;
  final String nomuser;
  final String? startstayuser;
  final String? endstayuser;
  final String? feedsuser;
  final Rank rank;
  final Establishment establishment;
  final UserPreferences? preferencesuser;

  User({required this.iduser, required this.nickuser, required this.emailuser, required this.phoneuser, required this.prenomuser, required this.nomuser, required this.startstayuser, required this.endstayuser, required this.rank, required this.establishment, required this.feedsuser, required this.preferencesuser});

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      iduser: json['iduser'],
      nickuser: json['nickuser'],
      emailuser: json['emailuser'],
      phoneuser: json['phoneuser'],
      prenomuser: json['prenomuser'],
      nomuser: json['nomuser'],
      startstayuser: json['startstayuser'],
      endstayuser: json['endstayuser'],
      feedsuser: json['feedsuser'],
      rank: Rank.fromJson(json['rank']),
      establishment: Establishment.fromJson(json['establishment']),
      preferencesuser: UserPreferences.fromJson(jsonDecode(json['preferencesuser']))
    );
  }
}