import 'package:thermassist_mobile/models/sirtaqui-communication-type.model.dart';

class SirtaquiCommunication {
  final String? coordonneesTelecom;
  final SirtaquiCommunicationType? typedaccesTelecom;

  SirtaquiCommunication({required this.coordonneesTelecom, required this.typedaccesTelecom});

  factory SirtaquiCommunication.fromJson(Map<String, dynamic> json) {
    return SirtaquiCommunication(
      coordonneesTelecom: json['coordonneesTelecom'],
      typedaccesTelecom: json['typedaccesTelecom'] != null ? SirtaquiCommunicationType.fromJson(json['typedaccesTelecom']) : null
    );
  }
}