class SirtaquiAddress {
  final String? adresse2;
  final String? codePostal;
  final String? commune;
  final String? raisonsociale;

  SirtaquiAddress({required this.adresse2, required this.codePostal, required this.commune, required this.raisonsociale});

  factory SirtaquiAddress.fromJson(Map<String, dynamic> json) {
    return SirtaquiAddress(
      adresse2: json['adresse2'],
      codePostal: json['codePostal'],
      commune: json['commune'],
      raisonsociale: json['raisonsociale'],
    );
  }
}