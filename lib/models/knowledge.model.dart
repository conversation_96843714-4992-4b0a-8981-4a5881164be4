class Knowledge {
  final int idknowledge;
  final String contentknowledge;
  final int? establishmentid;

  Knowledge({required this.idknowledge, required this.contentknowledge, this.establishmentid});

  factory Knowledge.fromJson(Map<String, dynamic> json) {
    return Knowledge(
        idknowledge: json['idknowledge'],
        contentknowledge: json['contentknowledge'],
        establishmentid: json['establishmentid']
    );
  }
}