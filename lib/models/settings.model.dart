class Settings {
  final int idsettings;
  final String? bannersettings;
  final String themesettings;
  final String? mailpicsettings;
  final String? documentsettings;
  final String dashboardsettings;
  final String servicessettings;
  final String? numbersettings;
  final String communicationsettings;
  final String? logosettings;
  final int darksettings;
  final String? emailbannersettings;

  Settings({required this.idsettings, required this.bannersettings, required this.themesettings, required this.mailpicsettings, required this.documentsettings, required this.dashboardsettings, required this.servicessettings,
    required this.numbersettings, required this.communicationsettings, required this.logosettings, required this.darksettings, required this.emailbannersettings});

  factory Settings.fromJson(Map<String, dynamic> json) {
    return Settings(
        idsettings: json['idsettings'],
        bannersettings: json['bannersettings'],
        themesettings: json['themesettings'],
        mailpicsettings: json['mailpicsettings'],
        documentsettings: json['documentsettings'],
        dashboardsettings: json['dashboardsettings'],
        servicessettings: json['servicessettings'],
        numbersettings: json['numbersettings'],
        communicationsettings: json['communicationsettings'],
        logosettings: json['logosettings'],
        darksettings: json['darksettings'],
        emailbannersettings: json['emailbannersettings']
    );
  }
}