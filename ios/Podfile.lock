PODS:
  - Firebase/CoreOnly (11.4.0):
    - FirebaseCore (= 11.4.0)
  - Firebase/Messaging (11.4.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.4.0)
  - firebase_core (3.8.1):
    - Firebase/CoreOnly (= 11.4.0)
    - Flutter
  - firebase_messaging (15.1.6):
    - Firebase/Messaging (= 11.4.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.4.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.6.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.4.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - geolocator_apple (1.2.0):
    - Flutter
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - Toast (4.1.1)
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - Toast

EXTERNAL SOURCES:
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  Firebase: cf1b19f21410b029b6786a54e9764a0cacad3c99
  firebase_core: 418aed674e9a0b8b6088aec16cde82a811f6261f
  firebase_messaging: 98619a0572d82cfb3668e78859ba9f1110e268c9
  FirebaseCore: e0510f1523bc0eb21653cac00792e1e2bd6f1771
  FirebaseCoreInternal: d98ab91e2d80a56d7b246856a8885443b302c0c2
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  FirebaseMessaging: f8a160d99c2c2e5babbbcc90c4a3e15db036aee2
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  fluttertoast: 723e187574b149e68e63ca4d39b837586b903cfa
  geolocator_apple: 9bcea1918ff7f0062d98345d238ae12718acfbc1
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  uni_links: d97da20c7701486ba192624d99bffaaffcfc298a
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  webview_flutter_wkwebview: 0982481e3d9c78fd5c6f62a002fcd24fc791f1e4

PODFILE CHECKSUM: 84a66a4bafabed8ac7b98ef63b1d312cc936c0fb

COCOAPODS: 1.12.1
